from datetime import datetime as dt
import datetime


def format_int_date_to_dot(date):
    '''
    将日期从数字格式转换为点分隔的格式
    :param date: 日期（数字格式，如：20210801）
    :return: 日期（点分隔的格式，如：2021.08.01）
    '''
    return f"{date[:4]}.{date[4:6]}.{date[6:]}"

def date_to_string(date):
    """

    :param date: 日期时间类型
    :return:  格式为yyyy-mm-dd HH:MM:SS的字符串
    """
    return date.strftime('%Y-%m-%d %H:%M:%S')


# 将YYYYMMDD格式的日期字符串转换为月份和日期字符串，例如：20230329 -> 3.29
def convert_date_to_month_day_str(date_str):
    """
    将YYYYMMDD格式的日期字符串转换为月份和日期字符串，例如：20230329 -> 3.29
    :param date_str: 日期字符串，格式为 "20230329"
    :return: 月份和日期字符串，例如：3.29
    """
    datetime_obj = datetime.datetime.strptime(date_str, "%Y%m%d")
    return datetime_obj.strftime("%-m.%-d")


def get_now_datetime_string():
    """
    获取当前时间字符串,例如： 2023-10-29 23:46:14
    :return:
    """
    return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')



def seconds_to_datetime_string(seconds):
    # 将时间戳转换为datetime对象
    dt_object = dt.fromtimestamp(seconds)

    # 将datetime对象格式化为'yyyy-mm-dd HH:MM:SS'的字符串
    formatted_time = dt_object.strftime('%Y-%m-%d %H:%M:%S')
    return formatted_time


def seconds_to_yyyymmdd_int(seconds):
    """"
    将秒数转换为YYYYMMDD格式的整数
    :param seconds:
    :return:
    """
    # 将时间戳转换为datetime对象
    dt_object = dt.fromtimestamp(seconds)

    # 将datetime对象格式化为'yyyy-mm-dd HH:MM:SS'的字符串
    formatted_time = dt_object.strftime('%Y%m%d')
    return int(formatted_time)


def seconds_to_time_string(seconds):

    if seconds is None:
        return None
    # 将时间戳转换为datetime对象
    dt_object = dt.fromtimestamp(seconds)

    # 将datetime对象格式化为'yyyy-mm-dd HH:MM:SS'的字符串
    formatted_time = dt_object.strftime('%H:%M:%S')
    return formatted_time


def convert_utc_to_local(utc_time):
    """
    将 UTC 时间转换为本地时间

    Args:
      utc_time: UTC 时间，格式为 "2023-10-29T23:46:14+08:00"

    Returns:
      本地时间，格式为 "2023-10-29 23:46:14"
    """

    local_timezone = datetime.timezone(datetime.timedelta(hours=8))
    local_time = datetime.datetime.strptime(utc_time, "%Y-%m-%dT%H:%M:%S%z")
    return local_time.astimezone(local_timezone).strftime("%Y-%m-%d %H:%M:%S")


def convert_time_to_date_yyyymmdd_str(time_str):
    """
    将时间字符串转换为日期字符串

    Args:
      time_str: 时间字符串，格式为 "2023-10-29 23:46:14"

    Returns:
      日期字符串，格式为 "20231029"
    """

    datetime_obj = datetime.datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
    return datetime_obj.strftime("%Y%m%d")


#
if __name__ == '__main__':
      print(get_now_datetime_string())
#     utc_time = "2023-10-29T23:46:14+08:00"
#     local_time = convert_utc_to_local(utc_time)
#     print(local_time)
#
#
#     print(seconds_to_datetime_string(1693733546))
#
#     print(convert_time_to_date_yyyymmdd_str("2023-10-29 23:46:14"))