from flask import request, jsonify
from flask import Blueprint
from error_code import <PERSON>rror<PERSON><PERSON>
from exception.biz_exception import BizException
import stringutils
from service import distribution_service
from sso import authentication

distributionBlueprint = Blueprint('distribution', __name__)

@distributionBlueprint.errorhandler(BizException)
def handle_biz_exception(error):
    # 获取异常对象中的 error_code 信息
    error_code = error.code  # 这里的 error 是捕获到的异常对象
    error_message = error.message  # 异常对象中的消息
    response = jsonify({
        'code': '0',
        'error_code': error_code,
        'error_msg': error_message,
    })
    response.status_code = 200
    return response



@distributionBlueprint.route('/distribution/get_distributor_info', methods=['GET'])
def distribution_get_distributor_info():
    """
    获取分销商信息
    :param user_id:
    :return:
    """
    if request.method == 'GET':
        app_key = request.headers.get('appKey')
        access_token = request.headers.get('sessionId')
        if app_key is None or stringutils.is_whitespace(app_key) \
                or stringutils.is_whitespace(access_token):
            return jsonify({
                'code': '0',
                'error_code': ErrorCode.PARAM_IS_EMPTY.code,
                'error_msg': ErrorCode.PARAM_IS_EMPTY.message,
            })
        session_user = authentication.get_session_user(access_token, app_key)
        user_id = session_user.user_id
        distributor_info = distribution_service.get_distributor_info(user_id)
        return jsonify({
            'code': '1',
            'data': distributor_info
        })


#用户提交推广大使申请
@distributionBlueprint.route('/distribution/apply', methods=['POST'])
def distribution_apply():
    """
    用户申请分销员
    :param name: 分销员姓名
    :param phone: 手机号
    :param verification_code: 验证码
    :param province_code: 省份编码
    :param city_code: 城市编码
    :param referral_code: 推荐码（邀请下级分销员则带上推荐码，否则不传）
    :param organization_code: 组织编码（如果是分公司拓展业务则带上组织编码，否则不传）
    :return:
    """
    if request.method == 'POST':
        data = request.json  # 获取 JOSN 数据
        name = data.get('name')  # 以字典形式获取参数
        phone = data.get('phone')
        verification_code = data.get('verification_code')
        # 推荐码
        referral_code = data.get('referral_code')
        # organization_code
        organization_code = data.get('organization_code')
        # 省份编码
        province_code = data.get('province_code')
        # 城市编码
        city_code = data.get('city_code')

        # 判断参数是否为空
        if name is None or stringutils.is_whitespace(name):
            raise BizException(ErrorCode.NAME_IS_EMPTY)
        # 判断手机号是否为空
        if phone is None or stringutils.is_whitespace(phone):
            raise BizException(ErrorCode.PHONE_IS_EMPTY)
        # 判断验证码是否为空
        if verification_code is None or stringutils.is_whitespace(verification_code):
            raise BizException(ErrorCode.VERIFICATION_CODE_IS_EMPTY)
        # 判断省份编码是否为空
        if province_code is None or stringutils.is_whitespace(province_code):
            raise BizException(ErrorCode.PROVINCE_IS_EMPTY)
        # 判断城市编码是否为空
        if city_code is None or stringutils.is_whitespace(city_code):
            raise BizException(ErrorCode.CITY_IS_EMPTY)
        # 如果组织代码为空，则设置为0000
        if organization_code is None or stringutils.is_whitespace(organization_code):
            organization_code = '0000'

        distribution_service.apply_distributor(name, phone, verification_code, province_code, city_code,
                                               referral_code, organization_code, request.headers.get("sessionId"), request.headers.get("appKey"))

        return jsonify({
            'code': '1'
        })



@distributionBlueprint.route('/distribution/get_commission_info', methods=['GET'])
def get_commission_info():
    """
    获取佣金信息
    :return:
    """
    if request.method == 'GET':
        app_key = request.headers.get('appKey')
        access_token = request.headers.get('sessionId')
        if app_key is None or stringutils.is_whitespace(app_key) \
                or stringutils.is_whitespace(access_token):
            return jsonify({
                'code': '0',
                'error_code': ErrorCode.PARAM_IS_EMPTY.code,
                'error_msg': ErrorCode.PARAM_IS_EMPTY.message,
            })
        session_user = authentication.get_session_user(access_token, app_key)
        commission_info = distribution_service.get_commission_info(session_user.user_id)
        return jsonify({
            'code': '1',
            'data': commission_info
        })



@distributionBlueprint.route('/distribution/get_my_distributor_base_info', methods=['GET'])
def get_my_distributor_base_info():
    """
    获取我的分销商基础信息
    :param user_id:
    :return:
    """
    if request.method == 'GET':
        app_key = request.headers.get('appKey')
        access_token = request.headers.get('sessionId')
        if app_key is None or stringutils.is_whitespace(app_key) \
                or stringutils.is_whitespace(access_token):
            return jsonify({
                'code': '0',
                'error_code': ErrorCode.PARAM_IS_EMPTY.code,
                'error_msg': ErrorCode.PARAM_IS_EMPTY.message,
            })
        session_user = authentication.get_session_user(access_token, app_key)
        user_id = session_user.user_id
        my_distributor_base_info = distribution_service.get_my_distributor_base_info(user_id)
        return jsonify({
            'code': '1',
            'data': my_distributor_base_info
        })



@distributionBlueprint.route('/distribution/get_my_distributor_list', methods=['GET'])
def get_my_distributor_list():
    """
    获取我的分销商列表
    :param user_id:
    :return:
    """
    if request.method == 'GET':
        app_key = request.headers.get('appKey')
        access_token = request.headers.get('sessionId')
        if app_key is None or stringutils.is_whitespace(app_key) \
                or stringutils.is_whitespace(access_token):
            return jsonify({
                'code': '0',
                'error_code': ErrorCode.PARAM_IS_EMPTY.code,
                'error_msg': ErrorCode.PARAM_IS_EMPTY.message,
            })
        session_user = authentication.get_session_user(access_token, app_key)
        user_id = session_user.user_id
        keyword = request.args.get('keyword')
        page_index = request.args.get('page_index')
        page_size = request.args.get('page_size')
        # 对page_index 和 page_size 进行判空并进行类型转换
        page_index = int(page_index) if page_index else 1
        page_size = int(page_size) if page_size else 20
        distributors_list = distribution_service.get_my_distributor_list(user_id, keyword, page_index, page_size)
        return jsonify({
            'code': '1',
            'data': distributors_list
        })



@distributionBlueprint.route('/distribution/get_my_customer_info', methods=['GET'])
def get_my_customer_info():
    """
    获取我的客户信息
    :param user_id:
    :return:
    """
    if request.method == 'GET':
        app_key = request.headers.get('appKey')
        access_token = request.headers.get('sessionId')
        if app_key is None or stringutils.is_whitespace(app_key) \
                or stringutils.is_whitespace(access_token):
            return jsonify({
                'code': '0',
                'error_code': ErrorCode.PARAM_IS_EMPTY.code,
                'error_msg': ErrorCode.PARAM_IS_EMPTY.message,
            })
        session_user = authentication.get_session_user(access_token, app_key)
        user_id = session_user.user_id
        my_customer_info = distribution_service.get_my_customer_info(user_id)
        return jsonify({
            'code': '1',
            'data': my_customer_info
        })



@distributionBlueprint.route('/distribution/get_my_customer_list', methods=['GET'])
def get_my_customer_list():
    """
    获取我的客户列表
    :param user_id:
    :return:
    """
    if request.method == 'GET':
        app_key = request.headers.get('appKey')
        access_token = request.headers.get('sessionId')
        if app_key is None or stringutils.is_whitespace(app_key) \
                or stringutils.is_whitespace(access_token):
            return jsonify({
                'code': '0',
                'error_code': ErrorCode.PARAM_IS_EMPTY.code,
                'error_msg': ErrorCode.PARAM_IS_EMPTY.message,
            })
        session_user = authentication.get_session_user(access_token, app_key)
        user_id = session_user.user_id
        keyword = request.args.get('keyword')
        page_index = request.args.get('page_index')
        page_size = request.args.get('page_size')
        # 对page_index 和 page_size 进行判空并进行类型转换
        page_index = int(page_index) if page_index else 1
        page_size = int(page_size) if page_size else 20
        customers_list = distribution_service.get_my_customer_list(user_id, keyword, page_index, page_size)
        return jsonify({
            'code': '1',
            'data': customers_list
        })



@distributionBlueprint.route('/distribution/get_my_distributor_order_list', methods=['GET'])
def get_my_distributor_order_list():
    """
    获取我的分销商订单列表
    :param user_id:
    :return:
    """
    if request.method == 'GET':
        app_key = request.headers.get('appKey')
        access_token = request.headers.get('sessionId')
        if app_key is None or stringutils.is_whitespace(app_key) \
                or stringutils.is_whitespace(access_token):
            return jsonify({
                'code': '0',
                'error_code': ErrorCode.PARAM_IS_EMPTY.code,
                'error_msg': ErrorCode.PARAM_IS_EMPTY.message,
            })
        session_user = authentication.get_session_user(access_token, app_key)
        user_id = session_user.user_id
        keyword = request.args.get('keyword')
        page_index = request.args.get('page_index')
        page_size = request.args.get('page_size')
        # 对page_index 和 page_size 进行判空并进行类型转换
        page_index = int(page_index) if page_index else 1
        page_size = int(page_size) if page_size else 20
        distributor_orders_list = distribution_service.get_my_distributor_order_list(user_id, keyword, page_index, page_size)
        return jsonify({
            'code': '1',
            'data': distributor_orders_list
        })



@distributionBlueprint.route('/distribution/get_my_settlement_record_list', methods=['GET'])
def get_my_settlement_record_list():
    """
    获取我的结算记录列表
    :param user_id:
    :return:
    """
    if request.method == 'GET':
        app_key = request.headers.get('appKey')
        access_token = request.headers.get('sessionId')
        if app_key is None or stringutils.is_whitespace(app_key) \
                or stringutils.is_whitespace(access_token):
            return jsonify({
                'code': '0',
                'error_code': ErrorCode.PARAM_IS_EMPTY.code,
                'error_msg': ErrorCode.PARAM_IS_EMPTY.message,
            })
        session_user = authentication.get_session_user(access_token, app_key)
        user_id = session_user.user_id
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        page_index = request.args.get('page_index')
        page_size = request.args.get('page_size')
        # 对page_index 和 page_size 进行判空并进行类型转换
        page_index = int(page_index) if page_index else 1
        page_size = int(page_size) if page_size else 20
        settlement_records_list = distribution_service.get_my_settlement_record_list(user_id, start_date, end_date, page_index, page_size)
        return jsonify({
            'code': '1',
            'data': settlement_records_list
        })
