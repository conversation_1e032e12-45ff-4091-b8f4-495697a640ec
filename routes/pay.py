from flask import Blueprint
from flask import request, jsonify
import json
import os
import time
from typing import Optional
from wechatpayv3 import WeChatPay, WeChatPayType

import error_code
from app_logconfig import logger
from app_logconfig import error_logger
from random import sample, choice
from string import ascii_letters, digits
from service import pay_service
from service import user_biz_service
from sso import authentication
from utils import code_gen_util, date_utils
from exception.biz_exception import BizException
from service import user_service
from models.user_wechat_account import UserWechatAccount
import config
import threading
from app_init import app
import itunesiap
from Crypto.Cipher import AES
from base64 import b64encode, b64decode
from alipay import AliPay
import json

payBlueprint = Blueprint('pay', __name__)


# 微信支付商户号（直连模式）或服务商商户号（服务商模式，即sp_mchid)
MCHID = '**********'

# 商户证书私钥
with open('/opt/tmp/chatbot/apiclient_key.pem') as f:
    PRIVATE_KEY = f.read()

# 商户证书序列号
CERT_SERIAL_NO = os.getenv("WECHAT_PAY_CERT_SERIAL_NO")

# 苹果开发者账号密码
APPLE_CONNECT_PASSWORD = os.getenv("APPLE_CONNECT_PASSWORD")

# API v3密钥， https://pay.weixin.qq.com/wiki/doc/apiv3/wechatpay/wechatpay3_2.shtml
APIV3_KEY = os.getenv("WECHAT_API_KEY")

# APPID，应用ID或服务商模式下的sp_appid
APPID = 'wxc3ecf3aa97e2d79e'

# 移动应用APPID
APPID_APP = 'wx8a882167ca93bd37'

# 回调地址，也可以在调用接口的时候覆盖
NOTIFY_URL = 'https://chat.xinquai.com/wechat_pay/notify'

# 微信支付平台证书缓存目录，减少证书下载调用次数，首次使用确保此目录为空目录.
# 初始调试时可不设置，调试通过后再设置，示例值:'./cert'
CERT_DIR = '/opt/tmp/chatbot'
# 接入模式:False=直连商户模式，True=服务商模式
PARTNER_MODE = False
PROXY = None
# 初始化
wxpay = WeChatPay(
    wechatpay_type=WeChatPayType.NATIVE,
    mchid=MCHID,
    private_key=PRIVATE_KEY,
    cert_serial_no=CERT_SERIAL_NO,
    apiv3_key=APIV3_KEY,
    appid=APPID,
    notify_url=NOTIFY_URL,
    cert_dir=CERT_DIR,
    logger=logger,
    partner_mode=PARTNER_MODE,
    proxy=PROXY)

app_wxpay = WeChatPay(
    wechatpay_type=WeChatPayType.APP,
    mchid=MCHID,
    private_key=PRIVATE_KEY,
    cert_serial_no=CERT_SERIAL_NO,
    apiv3_key=APIV3_KEY,
    appid=APPID_APP,
    notify_url=NOTIFY_URL,
    cert_dir=CERT_DIR,
    logger=logger,
    partner_mode=PARTNER_MODE,
    proxy=PROXY
)




# 支付回调URL
# WECHAT_NOTIFY_URL = "https://chat.xinquai.com/pay/wechat/notify"



# 微信支付V3相关配置信息
API_KEY = APIV3_KEY
CERTIFICATE_PATH = "/opt/tmp/chatbot/apiclient_cert.pem"
CERTIFICATE_KEY_PATH = "/opt/tmp/chatbot/apiclient_key.pem"


def nonce_str(length=32):
    """
    随机字符串
    :param length:
    :return:
    """
    char = ascii_letters + digits
    return "".join(choice(char) for _ in range(length))


@payBlueprint.errorhandler(BizException)
def handle_biz_exception(error):
    # 获取异常对象中的 error_code 信息
    error_code = error.code  # 这里的 error 是捕获到的异常对象
    error_message = error.message  # 异常对象中的消息
    response = jsonify({
        'code': '0',
        'error_code': error_code,
        'error_msg': error_message,
    })
    response.status_code = 200
    return response


def query_payment_result(out_trade_no):
    """
    查询支付结果
    :param out_trade_no:
    :return:
    """
    with app.app_context():
        query_count = 0
        while True:
            try:
                # 调用微信支付的查询结果接口
                http_status, content = wxpay.query(out_trade_no=out_trade_no)
                # 将content转换为json对象
                content = json.loads(content)
                # 如果http状态码不是200，说明查询失败
                if http_status != 200:
                    error_logger.error("error to query_order, http_status: %s" % http_status)
                    time.sleep(5)
                    query_count += 1
                # 检查支付结果
                if content['trade_state'] == 'SUCCESS':
                    # 支付成功，结束查询
                    # 更新支付流水状态为支付成功
                    user_biz_service.pay_success(out_trade_no, content['transaction_id'], content['amount']['total'],
                                                 date_utils.convert_utc_to_local(content['success_time']),
                                                 content['trade_type'], json.dumps(content, indent=4))
                    # 记录分销订单和进行实时清分
                    user_biz_service.add_distributor_order_and_clearing_thread(out_trade_no)
                    break
                elif query_count >= 36:
                    break
                else:
                    # 支付未成功，延迟5秒后再次查询
                    time.sleep(5)
                    query_count += 1
            except Exception as e:
                error_logger.error("error to query_order", exc_info=True)
                time.sleep(5)
                query_count += 1


@payBlueprint.route('/wechat_pay/jsapi', methods=['POST'])
def wechat_pay_pay_jsapi():
    logger.info("access_wechat_pay_jsapi")
    session_user = authentication.get_session_user(request.headers.get("sessionId"), request.headers.get("appKey"))
    # print(session_user)
    data = request.get_json()
    # 以jsapi下单为例，下单成功后，将prepay_id和其他必须的参数组合传递给JSSDK的wx.chooseWXPay接口唤起支付
    # out_trade_no = ''.join(sample(ascii_letters + digits, 8))
    out_trade_no = code_gen_util.get_uuid(session_user.user_id)
    package_type = data['package_type']
    description = data['body']
    promotion_code = data.get('promotion_code')
    discount_amount = data.get('discount_amount', 0)
    target_user_id = None
    # 如果promotion_code 不为空并且不为空字符串
    if promotion_code is not None and promotion_code != '':
        # 将promotion_code转换为大写
        promotion_code = promotion_code.upper()
        user = user_service.get_user_info_by_pcode_or_phone(promotion_code)
        if user is not None:
            target_user_id = user.id
        else:
            # 抛用户不存在的业务异常
            raise BizException(error_code.ErrorCode.USER_IS_NOT_EXIST)
    user_wechat_account = UserWechatAccount.query.filter_by(user_id=session_user.user_id).first()
    if user_wechat_account is None:
        raise BizException(error_code.ErrorCode.USER_WECHAT_ACCOUNT_NOT_EXIST)
    payer = {'openid': user_wechat_account.openid}
    if package_type == 0:
        actual_fee = 100
        total_fee = 100
    elif package_type == 1:
        actual_fee = 5800
        total_fee = 5800
    elif package_type == 2:
        actual_fee = 12800
        total_fee = 12800
    elif package_type == 3:
        actual_fee = 29800
        total_fee = 29800
    elif package_type == 4:
        actual_fee = 39800
        total_fee = 39800
    elif package_type == 5:
        actual_fee = 42800
        total_fee = 42800
    else:
        return jsonify({
            'code': 0,
            'error_msg': 'package_type参数不合法'
        })
    # 如果环境不是release,支付金额为1分
    if config.ENV != "release":
        actual_fee = 1
        total_fee = 1
    else:
       # Bruce线上测试
       if session_user.phone == '***********':
              actual_fee = 1
              total_fee = 1
    code, message = wxpay.pay(
        description=description,
        out_trade_no=out_trade_no,
        amount={'total': actual_fee},
        pay_type=WeChatPayType.JSAPI,
        payer=payer
    )
    result = json.loads(message)
    # 记录result日志，方便排查问题
    logger.info(result)
    if code in range(200, 300):
        prepay_id = result.get('prepay_id')
        timestamp = int(time.time())
        noncestr = nonce_str()
        package = 'prepay_id=' + prepay_id
        paysign = wxpay.sign([APPID, str(timestamp), noncestr, package])
        signtype = 'RSA'
        ## 写入支付流水
        id = pay_service.add_payment_flow(session_user.user_id, data['package_type'], out_trade_no,
                                          total_fee, actual_fee,  discount_amount, 1, prepay_id, None, None, None, None, target_user_id)
        # 创建一个新的线程来查询支付结果
        threading.Thread(target=query_payment_result, args=(out_trade_no,)).start()
        if id is not None:
            return jsonify({'code': 1, 'data': {
                'appId': APPID,
                'timeStamp': timestamp,
                'nonceStr': noncestr,
                'package': 'prepay_id=%s' % prepay_id,
                'signType': signtype,
                'paySign': paysign
            }})
    else:
        payerJsonString = json.dumps(payer, indent=4)
        error_logger.error("error to pay_jsapi, code: %s, payer: %s" % (code, payerJsonString))
        return jsonify({
            'code': 0,
            'error_msg': {'reason': result.get('code')}
        })


@payBlueprint.route('/wechat_pay/notify', methods=['POST'])
def notify():
    logger.info("access_wechat_pay_notify")

    headers = request.headers

    # 打印请求的headers
    #print("Request Headers:")
    for key, value in headers.items():
        print(f"{key}: {value}")

    result = wxpay.callback(request.headers, request.data)
    logger.info(result)
    if result and result.get('event_type') == 'TRANSACTION.SUCCESS':
        resp = result.get('resource')
        appid = resp.get('appid')
        mchid = resp.get('mchid')
        out_trade_no = resp.get('out_trade_no')
        transaction_id = resp.get('transaction_id')
        trade_type = resp.get('trade_type')
        trade_state = resp.get('trade_state')
        trade_state_desc = resp.get('trade_state_desc')
        bank_type = resp.get('bank_type')
        attach = resp.get('attach')
        success_time = resp.get('success_time')
        payer = resp.get('payer')
        amount = resp.get('amount').get('total')
        resp_json_string = json.dumps(resp, indent=4)
        logger.info(resp_json_string)
        user_biz_service.pay_success(out_trade_no, transaction_id, amount, date_utils.convert_utc_to_local(success_time), trade_type, resp_json_string)
        # 记录分销订单和进行实时清分
        user_biz_service.add_distributor_order_and_clearing_thread(out_trade_no)
        return jsonify({'code': '1', 'message': '成功'})
    else:
        error_logger.error("error to pay_notify, result: %s" % result)
        return jsonify({'code': '0', 'message': '失败'})



@payBlueprint.route('/pay/verify_ios_receipt', methods=['POST'])
def verify_ios_receipt():
    """
    验证苹果内购 receipt
    TODO 添加 discount_amount 逻辑
    :return:
    """
    receipt_data = request.json.get('receipt_data').get('verificationData').get('serverVerificationData')
    purchaseID = request.json.get('receipt_data').get('purchaseID')
    productID = request.json.get('receipt_data').get('productID')
    # 毫秒时间戳
    transactionDate = request.json.get('receipt_data').get('transactionDate')
    # logger.info("receipt_data: %s" % receipt_data)
    # logger.info("purchaseID: %s" % purchaseID)
    # logger.info("productID: %s" % productID)
    # logger.info("transactionDate: %s" % transactionDate)
    # 将transactionDate变为整数
    transactionDate = int(transactionDate)
    # 将毫秒时间戳转换为字符串yyyy-MM-dd HH:mm:ss
    transactionDate = date_utils.seconds_to_datetime_string(transactionDate/1000)
    try:
        session_user = authentication.get_session_user(request.headers.get("sessionId"), request.headers.get("appKey"))
        if session_user is None:
            raise BizException(error_code.ErrorCode.VERIFY_TOKEN_ERROR)
        # 日志记录session_user的user_id
        logger.info("session_user_id: %s" % session_user.user_id)
        # logger.info(config.ENV != "release")
        # logger.info(config.ENV)

        if config.ENV != "release":
            logger.info("request apple sandbox")
            response = itunesiap.verify(receipt_data, password=APPLE_CONNECT_PASSWORD, exclude_old_transactions=True, use_sandbox=True)
        else:
            logger.info("request apple production")
            response = itunesiap.verify(receipt_data, password=APPLE_CONNECT_PASSWORD, exclude_old_transactions=True, use_sandbox=False)
        logger.info("response: %s" % response)

    except itunesiap.exc.InvalidReceipt as e:
        if config.ENV == 'release':
            # 检查是否是 21007 错误码
            if e.status == 21007:
                # 如果是 21007 错误码，尝试在沙盒环境中验证
                logger.info("status is 21007, request apple sandbox")
                response = itunesiap.verify(receipt_data, password=APPLE_CONNECT_PASSWORD, exclude_old_transactions=True, use_sandbox=True)
                # return response
            else:
                error_logger.error("An error occurred, env is release:", exc_info=True)
                return jsonify(
                    {'code':'0','error_code': error_code.ErrorCode.INVALID_PAYMENT_RECEIPT.code, 'error_msg': error_code.ErrorCode.INVALID_PAYMENT_RECEIPT.message}
                )
        else:
            error_logger.error("An error occurred, env is sandbox:", exc_info=True)
            return jsonify(
                {'code':'0','error_code': error_code.ErrorCode.INVALID_PAYMENT_RECEIPT.code, 'error_msg': error_code.ErrorCode.INVALID_PAYMENT_RECEIPT.message}
            )

    # 从response 获取 bundle_id进行校验
    receipt = response.receipt
    if receipt.bundle_id != "com.xinqu.kouyu":
        error_logger.error("INVALID_PAYMENT_RECEIPT")
        return jsonify(
            {'code':'0','error_code': error_code.ErrorCode.INVALID_PAYMENT_RECEIPT.code, 'error_msg': error_code.ErrorCode.INVALID_PAYMENT_RECEIPT.message}
        )

    # 验证第一个交易
    last_in_app = receipt.last_in_app
    if last_in_app is None:
        error_logger.error("INVALID_PAYMENT_RECEIPT")
        return jsonify(
            {'code':'0','error_code': error_code.ErrorCode.INVALID_PAYMENT_RECEIPT.code, 'error_msg': error_code.ErrorCode.INVALID_PAYMENT_RECEIPT.message}
        )

    product_id = last_in_app.product_id

    purchaseID = last_in_app.transaction_id

    if not _is_valid_product_id(product_id):
        error_logger.error("INVALID_PAYMENT_RECEIPT")
        return jsonify(
            {'code':'0','error_code': error_code.ErrorCode.INVALID_PAYMENT_RECEIPT.code, 'error_msg': error_code.ErrorCode.INVALID_PAYMENT_RECEIPT.message}
        )

    if product_id == 'xiaoxikouyu_monthly_non_renewable_subscription':
         # Increase membership by 1 month
         #days = 30
         package_type = 1
         actual_fee = 5800
    elif product_id == 'xiaoxikouyu_quarterly_non_renewable_subscription':
        # Increase membership by 3 months
        #days = 90
        package_type = 2
        actual_fee = 12800
    elif product_id == 'xiaoxikouyu_annual_non_renewable_subscription':
        # Increase membership by 1 year
        #days = 365
        package_type = 3
        actual_fee = 29800
    elif product_id == 'xiaoxikouyu_two_year_non_renewable_subscription':
        # Increase membership by 2 years
        #days = 730
        package_type = 4
        actual_fee = 39800
    elif product_id == 'xiaoxikouyu_3_year_non_renewable_subscription':
        # Increase membership by 2 years
        #days = 730
        package_type = 5
        actual_fee = 42800
    else:
        return jsonify({
            'code': '0',
            'error_code': error_code.ErrorCode.INVALID_PRODUCT_ID,  # 未知的产品ID
            'error_msg': error_code.ErrorCode.INVALID_PRODUCT_ID.message
        })
        # 获取支付金额
        # 通过transaction_id来做幂等，如果插入报错，说明已经处理过这个订单
    id = pay_service.add_payment_flow(session_user.user_id, package_type, purchaseID,
                                          actual_fee, actual_fee,  0, 3, None, None, None, None, None)
    if id is not None:
        user_biz_service.pay_success(purchaseID, purchaseID, actual_fee, transactionDate, None, receipt_data)

        # 记录分销订单和进行实时清分
        user_biz_service.add_distributor_order_and_clearing_thread(out_trade_no)

        return jsonify({
            'code': '1'
        })



@payBlueprint.route('/app/wx_pre_pay', methods=['POST'])
def appWxPrePay():
    logger.info("appWxPrePay")
    session_user = authentication.get_session_user(request.headers.get("sessionId"), request.headers.get("appKey"))
    # print(session_user)
    if session_user is None:
        return jsonify({
            'code': 0,
            'error_msg': '请先登录'
        })
    data = request.get_json()
    # 以jsapi下单为例，下单成功后，将prepay_id和其他必须的参数组合传递给JSSDK的wx.chooseWXPay接口唤起支付
    # out_trade_no = ''.join(sample(ascii_letters + digits, 8))
    out_trade_no = code_gen_util.get_uuid(session_user.user_id)
    package_type = data['package_type']
    description = data['body']
    promotion_code = data.get('promotion_code')
    discount_amount = data.get('discount_amount', 0)
    target_user_id = None
    # 如果promotion_code 不为空并且不为空字符串
    if promotion_code is not None and promotion_code != '':
        # 将promotion_code转换为大写
        promotion_code = promotion_code.upper()
        user = user_service.get_user_info_by_pcode_or_phone(promotion_code)
        if user is not None:
            target_user_id = user.id
        else:
            # 抛用户不存在的业务异常
            raise BizException(error_code.ErrorCode.USER_IS_NOT_EXIST)
    user_wechat_account = UserWechatAccount.query.filter_by(user_id=session_user.user_id).first()
    if user_wechat_account is None:
        raise BizException(error_code.ErrorCode.USER_WECHAT_ACCOUNT_NOT_EXIST)
    payer = {'openid': user_wechat_account.openid}
    if package_type == 0:
        actual_fee = 100
        total_fee = 100
    elif package_type == 1:
        actual_fee = 5800
        total_fee = 5800
    elif package_type == 2:
        actual_fee = 12800
        total_fee = 12800
    elif package_type == 3:
        actual_fee = 29800
        total_fee = 29800
    elif package_type == 4:
        actual_fee = 39800
        total_fee = 39800
    elif package_type == 5:
        actual_fee = 42800
        total_fee = 42800
    else:
        return jsonify({
            'code': 0,
            'error_msg': 'package_type参数不合法'
        })
    # 如果环境不是release,支付金额为1分
    if config.ENV != "release":
        actual_fee = 1
        total_fee = 1
    else:
       # Bruce线上测试
       if session_user.phone == '***********':
              actual_fee = 1
              total_fee = 1
    code, message = wxpay.pay(
        description=description,
        out_trade_no=out_trade_no,
        amount={'total': actual_fee},
        pay_type=WeChatPayType.APP
    )
    result = json.loads(message)
    # 记录result日志，方便排查问题
    logger.info(result)
    if code in range(200, 300):
        prepay_id = result.get('prepay_id')
        timestamp = int(time.time())
        noncestr = nonce_str()
        paysign = app_wxpay.sign([APPID_APP, str(timestamp), noncestr, prepay_id])
        ## 写入支付流水
        id = pay_service.add_payment_flow(session_user.user_id, data['package_type'], out_trade_no,
                                          total_fee, actual_fee,  discount_amount, 1, prepay_id, None, None, None, target_user_id)
        # 创建一个新的线程来查询支付结果
        threading.Thread(target=query_payment_result, args=(out_trade_no,)).start()
        if id is not None:
            return jsonify({'code': 1, 'data': {
                'appid': APPID_APP,
                'partnerid': MCHID,
                'prepayid': prepay_id,
                'package': 'Sign=WXPay',
                'nonceStr': noncestr,
                'timestamp': timestamp,
                'sign': paysign
            }})
    else:
        payerJsonString = json.dumps(payer, indent=4)
        error_logger.error("error to pay_jsapi, code: %s, payer: %s" % (code, payerJsonString))
        return jsonify({
            'code': 0,
            'error_msg': {'reason': result.get('code')}
        })

def _is_valid_product_id(product_id) -> bool:
    """
    验证产品ID是否有效
    需要根据实际情况实现
    """
    if not product_id:
        return False

    # 这里添加您的产品ID验证逻辑
    valid_product_ids = {"xiaoxikouyu_monthly_non_renewable_subscription", "xiaoxikouyu_quarterly_non_renewable_subscription",
                         "xiaoxikouyu_annual_non_renewable_subscription", "xiaoxikouyu_two_year_non_renewable_subscription",
                         "xiaoxikouyu_3_year_non_renewable_subscription"}
    return product_id in valid_product_ids



# 支付宝相关配置

alipay_env = "release"

# 如果是正式环境
if alipay_env == "release":
    ALIPAY_APPID = "2021005122639498"
    app_private_key_file_name = "app_private_key.txt"
    alipay_public_key_file_name = "alipay_public_key.txt"
else:
    # 如果是测试环境
    ALIPAY_APPID = "9021000144678005"
    app_private_key_file_name = "sandbox_app_private_key.txt"
    alipay_public_key_file_name = "sandbox_alipay_public_key.txt"
# 定义密钥文件路径
CERT_DIR = '/opt/tmp/chatbot'

APP_PRIVATE_KEY_PATH = os.path.join(CERT_DIR, app_private_key_file_name)
ALIPAY_PUBLIC_KEY_PATH = os.path.join(CERT_DIR, alipay_public_key_file_name)

# 读取密钥文件
with open(APP_PRIVATE_KEY_PATH) as f:
    app_private_key_string = f.read()

with open(ALIPAY_PUBLIC_KEY_PATH) as f:
    alipay_public_key_string = f.read()


ALIPAY_NOTIFY_URL = ""
# 如果是正式环境
if alipay_env == "release":
    ALIPAY_NOTIFY_URL = "https://chat.xinquai.com/alipay/notify"
else:
    ALIPAY_NOTIFY_URL = "http://chat-test.xinquai.com/alipay/notify"

# 初始化支付宝客户端
alipay_client = AliPay(
    appid=ALIPAY_APPID,
    app_notify_url=ALIPAY_NOTIFY_URL,
    app_private_key_string=app_private_key_string,
    alipay_public_key_string=alipay_public_key_string,
    sign_type="RSA2",
    debug=False if alipay_env == "release" else True
)

def aes_encrypt(data: str, key: str = 'ABCDEFG') -> str:
    """AES加密"""
    # 将key填充到16位
    key = key.ljust(16, '\0')
    # 将数据填充到16的倍数
    pad = 16 - len(data) % 16
    data = data + pad * chr(pad)
    # 创建加密器
    cipher = AES.new(key.encode('utf-8'), AES.MODE_ECB)
    # 加密
    encrypted_data = cipher.encrypt(data.encode('utf-8'))
    return b64encode(encrypted_data).decode('utf-8')

def aes_decrypt(encrypted_data: str, key: str = 'ABCDEFG') -> str:
    """AES解密"""
    key = key.ljust(16, '\0')
    # base64解码
    encrypted_data = b64decode(encrypted_data)
    cipher = AES.new(key.encode('utf-8'), AES.MODE_ECB)
    # 解密
    decrypted_data = cipher.decrypt(encrypted_data)
    # 去除填充
    pad = decrypted_data[-1]
    return decrypted_data[:-pad].decode('utf-8')


def query_alipay_result(out_trade_no):
    """
    查询支付宝支付结果
    :param out_trade_no: 商户订单号
    :return:
    """
    with app.app_context():
        query_count = 0
        while True:
            try:
                # 调用支付宝查询接口
                response = alipay_client.api_alipay_trade_query(out_trade_no=out_trade_no)

                # 记录查询日志
                logger.info(f"查询支付宝订单状态: {response}")

                if response.get("code") == "10000":  # 接口调用成功
                    trade_status = response.get("trade_status")
                    if trade_status in ("TRADE_SUCCESS", "TRADE_FINISHED"):
                        # 支付成功，更新订单状态
                        user_biz_service.pay_success(
                            out_trade_no,
                            response.get("trade_no"),
                            int(float(response.get("total_amount")) * 100),  # 转换为分
                            response.get("send_pay_date"),
                            "APPPAY",
                            json.dumps(response, indent=4)
                        )
                        break
                    elif trade_status in ("WAIT_BUYER_PAY",):
                        # 等待支付，继续查询
                        if query_count >= 36:  # 最多查询3分钟(5秒 * 36次)
                            break
                        time.sleep(5)
                        query_count += 1
                    else:
                        # 其他状态说明支付失败，终止查询
                        error_logger.error(f"支付宝订单{out_trade_no}支付失败: {trade_status}")
                        break
                else:
                    # 查询接口调用失败
                    error_logger.error(f"查询支付宝订单{out_trade_no}状态失败: {response}")
                    if query_count >= 36:
                        break
                    time.sleep(5)
                    query_count += 1

            except Exception as e:
                error_logger.error(f"查询支付宝订单{out_trade_no}发生异常", exc_info=True)
                if query_count >= 36:
                    break
                time.sleep(5)
                query_count += 1



@payBlueprint.route('/alipay/create_order', methods=['POST'])
def create_alipay_order():
    """创建支付宝订单"""
    logger.info("access_alipay_create_order")
    session_user = authentication.get_session_user(request.headers.get("sessionId"), request.headers.get("appKey"))
    if session_user is None:
        return jsonify({
            'code': 0,
            'error_msg': '请先登录'
        })
    data = request.get_json()

    out_trade_no = code_gen_util.get_uuid(session_user.user_id)
    package_type = data.get('package_type')
    promotion_code = data.get('promotion_code')
    discount_amount = data.get('discount_amount', 0)
    description = ""

    # 计算支付金额
    if package_type == 0:
        actual_fee = 1.00
        total_fee = 1.00
        description = "3天会员"
    elif package_type == 1:
        actual_fee = 58.00
        total_fee = 58.00
        description = "月度会员"
    elif package_type == 2:
        actual_fee = 128.00
        total_fee = 128.00
    elif package_type == 3:
        actual_fee = 298.00
        total_fee = 298.00
        description = "季度会员"
    elif package_type == 3:
        actual_fee = 298.00
        total_fee = 298.00
        description = "年度会员"
    elif package_type == 4:
        actual_fee = 398.00
        total_fee = 398.00
        description = "两年会员"
    elif package_type == 5:
        actual_fee = 428.00
        total_fee = 428.00
        description = "三年会员"
    else:
        return jsonify({
            'code': 0,
            'error_msg': 'package_type参数不合法'
        })

    # 测试环境金额改为0.01
    if config.ENV != "release":
        actual_fee = 0.01
        total_fee = 0.01
    # 如果用户手机号是15158831110 或者是 ***********
    if session_user.phone == '15158831110' or session_user.phone == '***********':
        actual_fee = 0.01
        total_fee = 0.01

    try:
        target_user_id = None
        # 如果promotion_code 不为空并且不为空字符串
        if promotion_code is not None and promotion_code != '':
            # 将promotion_code转换为大写
            promotion_code = promotion_code.upper()
            user = user_service.get_user_info_by_pcode_or_phone(promotion_code)
            if user is not None:
                target_user_id = user.id
            else:
                # 抛用户不存在的业务异常
                raise BizException(error_code.ErrorCode.USER_IS_NOT_EXIST)


        # 创建支付宝订单
        order_string = alipay_client.api_alipay_trade_app_pay(
            out_trade_no=out_trade_no,
            total_amount=str(actual_fee),
            subject=description,
            notify_url=ALIPAY_NOTIFY_URL
        )

        # 写入支付流水
        id = pay_service.add_payment_flow(
            session_user.user_id,
            package_type,
            out_trade_no,
            int(total_fee * 100),  # 转换为分
            int(actual_fee * 100),  # 转换为分
            discount_amount,
            2,  # 支付宝支付类型
            2,
            None,
            None,
            None,
            target_user_id
        )

        if id is not None:
            # 启动异步查询线程
            threading.Thread(target=query_alipay_result, args=(out_trade_no,)).start()

            # 加密订单信息
            #encrypted_order_string = aes_encrypt(order_string)
            return jsonify({
                'code': 1,
                'data': {
                    'order_string': order_string
                }
            })

    except Exception as e:
        error_logger.error("创建支付宝订单失败", exc_info=True)
        return jsonify({
            'code': 0,
            'error_msg': '创建订单失败'
        })



@payBlueprint.route('/alipay/create_page_order', methods=['POST'])
def create_page_alipay_order():
    """创建支付宝网页订单"""
    logger.info("access_alipay_create_order")
    session_user = authentication.get_session_user(request.headers.get("sessionId"), request.headers.get("appKey"))
    data = request.get_json()

    out_trade_no = code_gen_util.get_uuid(session_user.user_id)
    package_type = data.get('package_type')
    promotion_code = data.get('promotion_code')
    discount_amount = data.get('discount_amount', 0)
    description = ""

    # 计算支付金额
    if package_type == 0:
        actual_fee = 1.00
        total_fee = 1.00
        description = "3天会员"
    elif package_type == 1:
        actual_fee = 58.00
        total_fee = 58.00
        description = "月度会员"
    elif package_type == 2:
        actual_fee = 128.00
        total_fee = 128.00
    elif package_type == 3:
        actual_fee = 298.00
        total_fee = 298.00
        description = "季度会员"
    elif package_type == 3:
        actual_fee = 298.00
        total_fee = 298.00
        description = "年度会员"
    elif package_type == 4:
        actual_fee = 398.00
        total_fee = 398.00
        description = "两年会员"
    elif package_type == 5:
        actual_fee = 428.00
        total_fee = 428.00
        description = "三年会员"
    else:
        return jsonify({
            'code': 0,
            'error_msg': 'package_type参数不合法'
        })

    # 测试环境金额改为0.01
    if config.ENV != "release":
        actual_fee = 0.01
        total_fee = 0.01

    try:
        target_user_id = None
        # 如果promotion_code 不为空并且不为空字符串
        if promotion_code is not None and promotion_code != '':
            # 将promotion_code转换为大写
            promotion_code = promotion_code.upper()
            user = user_service.get_user_info_by_pcode_or_phone(promotion_code)
            if user is not None:
                target_user_id = user.id
            else:
                # 抛用户不存在的业务异常
                raise BizException(error_code.ErrorCode.USER_IS_NOT_EXIST)


        # 创建支付宝订单
        order_string = alipay_client.api_alipay_trade_page_pay(
            out_trade_no=out_trade_no,
            total_amount=str(actual_fee),
            subject=description,
            notify_url=ALIPAY_NOTIFY_URL
        )

        # 写入支付流水
        id = pay_service.add_payment_flow(
            session_user.user_id,
            package_type,
            out_trade_no,
            int(total_fee * 100),  # 转换为分
            int(actual_fee * 100),  # 转换为分
            discount_amount,
            2,  # 支付宝支付类型
            2,
            None,
            None,
            None,
            target_user_id
        )

        if id is not None:
            # 启动异步查询线程
            threading.Thread(target=query_alipay_result, args=(out_trade_no,)).start()

            # 加密订单信息
            #encrypted_order_string = aes_encrypt(order_string)
            return jsonify({
                'code': 1,
                'data': {
                    'order_string': order_string
                }
            })

    except Exception as e:
        error_logger.error("创建支付宝订单失败", exc_info=True)
        return jsonify({
            'code': 0,
            'error_msg': '创建订单失败'
        })


@payBlueprint.route('/alipay/notify', methods=['POST'])
def alipay_notify():
    """支付宝支付回调"""
    logger.info("access_alipay_notify")

    data = request.form.to_dict()
    signature = data.pop("sign")

    try:
        # 验证签名
        if alipay_client.verify(data, signature):
            # 验证订单状态
            if data["trade_status"] in ("TRADE_SUCCESS", "TRADE_FINISHED"):
                out_trade_no = data["out_trade_no"]
                trade_no = data["trade_no"]
                total_amount = float(data["total_amount"]) * 100  # 转换为分
                gmt_payment = data["gmt_payment"]

                # 更新订单状态
                user_biz_service.pay_success(
                    out_trade_no,
                    trade_no,
                    int(total_amount),
                    gmt_payment,
                    "ALIPAY",
                    json.dumps(data, indent=4)
                )

                return "success"

        return "fail"

    except Exception as e:
        error_logger.error("支付宝回调处理失败", exc_info=True)
        return "fail"