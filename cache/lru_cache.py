import sys
from collections import OrderedDict
import time
from cache import cache_key_constant


class LRUCache:
    def __init__(self, capacity_in_bytes):
        self.capacity = capacity_in_bytes
        self.cache = OrderedDict()

    def get(self, key):
        if key in self.cache:
            self.cache.move_to_end(key)
            return self.cache[key][0]
        else:
            return None

    def put(self, key, value, expiration_time=None):
        if key in self.cache:
            self.cache.move_to_end(key)
        self.cache[key] = (value, self._calculate_expiration_time(expiration_time))

        while sys.getsizeof(self.cache) > self.capacity:
            self.cache.popitem(last=False)

        self.remove_expired_items()

    def remove(self, key):
        if key in self.cache:
            del self.cache[key]

    def __repr__(self):
        return repr(self.cache)

    def is_expired(self, key):
        if key in self.cache:
            expiration_time = self.cache[key][1]
            if expiration_time is not None and expiration_time < time.time():
                return True
        return False

    def remove_expired_items(self):
        expired_keys = [key for key in self.cache if self.is_expired(key)]
        for key in expired_keys:
            del self.cache[key]

    def _calculate_expiration_time(self, expiration_time):
        if expiration_time is None:
            return None
        return time.time() + expiration_time


# 将user_session容量设置为 30MB
user_session = LRUCache(30 * 1024 * 1024)  # 30MB

# 将每个用户的conversation对象缓存起来，
conversation_cache = LRUCache(100 * 1024 * 1024)  # 100MB

# 将每个用户某一个会话的conversation_memory对象缓存起来，
memory_cache = LRUCache(100 * 1024 * 1024)  # 100MB

# 将每个用户某一个会话的conversation_summary_cache对象缓存起来，
conversation_summary_cache = LRUCache(100 * 1024 * 1024)  # 100MB

# 将user_session容量设置为 30MB
user_settings = LRUCache(30 * 1024 * 1024)  # 30MB

# 将user容量设置为 30MB
user = LRUCache(30 * 1024 * 1024)  # 30MB

# common_cache
common_cache = LRUCache(30 * 1024 * 1024)  # 30MB


def get(key):
    return common_cache.get(key)


def set(key, value, expiration_seconds):
    common_cache.put(key, value, expiration_seconds)


def delete(key):
    common_cache.remove(key)


def get_user(user_id):
    key = str(user_id) + cache_key_constant.USER_SUFFIX
    return user.get(key)


def set_user(user_id, value):
    key = str(user_id) + cache_key_constant.USER_SUFFIX
    user.put(key, value, 7 * 24 * 60 * 60)


def delete_user(user_id):
    key = str(user_id) + cache_key_constant.USER_SUFFIX
    user.remove(key)


def get_user_settings(user_id):
    key = str(user_id) + cache_key_constant.USER_SETTINGS_SUFFIX
    return user_settings.get(key)


def set_user_settings(user_id, value):
    key = str(user_id) + cache_key_constant.USER_SETTINGS_SUFFIX
    user_session.put(key, value, 7 * 24 * 60 * 60)


def delete_user_settings(user_id):
    key = str(user_id) + cache_key_constant.USER_SETTINGS_SUFFIX
    user_settings.remove(key)


# 获取
def get_conversation(user_id, conversation_id):
    key = str(user_id) + str(conversation_id)
    cp = conversation_cache.get(key)
    return cp


def get_user_session(token_key):
    return user_session.get(token_key)


def get_memory(user_id, conversation_id):
    key = str(user_id) + str(conversation_id)
    cp = memory_cache.get(key)
    return cp


def put_memory(user_id, conversation_id, memory):
    key = str(user_id) + str(conversation_id)
    memory_cache.put(key, memory, 1 * 60 * 60)


def set_user_session(token_key, value):
    """
    设置用户user_session对象，expiration秒后过期
    :param token_key:
    :param value:
    :param expiration:
    :return:
    """
    user_session.put(token_key, value, 7 * 24 * 60 * 60)


def delete_user_session(token_key):
    user_session.remove(token_key)


def put_conversation(user_id, conversation_id, conversation):
    key = str(user_id) + str(conversation_id)
    conversation_cache.put(key, conversation, 3 * 60 * 60)


# 获取
def get_conversation_summary(user_id, conversation_id):
    key = str(user_id) + str(conversation_id)
    cp = conversation_summary_cache.get(key)
    return cp


def put_conversation_summary(user_id, conversation_id, summary):
    key = str(user_id) + str(conversation_id)
    conversation_summary_cache.put(key, summary, 3 * 60 * 60)
