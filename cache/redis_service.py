import redis

import config

# 创建Redis客户端
redis_host = config.REDIS_HOST
redis_port = config.REDIS_PORT  # 默认的Redis端口号
redis_password = config.REDIS_PASSWORD  # 如果有密码的话
env = config.ENV
if env == 'release' or env == 'trail':
    redis_db = 0  # Redis数据库索引，默认为0
elif env == 'develop':
    redis_db = 15
r = redis.Redis(host=redis_host, port=redis_port, password=redis_password, db=redis_db)


def get(key):
    """
    Return the value at key ``name``, or None if the key doesn't exist

    For more information see https://redis.io/commands/get
    """
    return r.get(key)


def setex(key, expiration, value):
    """
    设置过期时间为expiration的缓存
    :param key:
    :param value:
    :param expiration: 秒 represented by an integer 或者 a Python timedelta object
    :return:
    """
    r.setex(key, expiration, value)




def delete(key):
    """
    Delete one or more keys specified by ``names``
    """
    r.delete(key)


# if __name__ == '__main__':
#
#     t1 = time.time()
#
#     key = '12_222'
#     setex(key, "1212131313", 120)
#
#     print(get(key))
#
#     delete(key)
#
#     print(get(key))
#
#     print('cost of time :' +  str(time.time() - t1))