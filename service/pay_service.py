from models.payment_flow import PaymentFlow
from db import db
from exception.biz_exception import BizException
from error_code import ErrorCode
from sqlalchemy import update
from sqlalchemy import asc
from utils import date_utils
from sqlalchemy import func


def query_success_payment_flow(pay_date):
    '''
    查询支付成功的支付流水
    :param pay_date:
    :return:
    '''
    payment_flows = PaymentFlow.query.filter_by(pay_date=pay_date, pay_status=1).all()
    return payment_flows


def query_sum_month_payment_flow(pay_month):
    '''
    查询月份pay_month支付成功总金额
    :param pay_date:
    :return:
    '''
    # 截取pay_date前6位等于pay_month
    total_actual_fee = db.session.query(func.sum(PaymentFlow.actual_fee)).filter(func.substr(PaymentFlow.pay_date, 1, 6) == pay_month, PaymentFlow.pay_status==1).scalar()
    return total_actual_fee


def query_sum_year_payment_flow(pay_year):
    '''
    查询年份pay_year支付成功总金额
    :param pay_date:
    :return:
    '''
    # 截取pay_date前4位等于pay_year
    total_actual_fee = db.session.query(func.sum(PaymentFlow.actual_fee)).filter(func.substr(PaymentFlow.pay_date, 1, 4) == pay_year, PaymentFlow.pay_status==1).scalar()
    return total_actual_fee


def query_sum_all_payment_flow():
    '''
    查询所有支付成功总金额
    :param pay_date:
    :return:
    '''
    total_actual_fee = db.session.query(func.sum(PaymentFlow.actual_fee)).filter(PaymentFlow.pay_status==1).scalar()
    return total_actual_fee

def add_payment_flow(user_id, business_type, out_trade_no, total_fee, actual_fee, discount_amount,
                     transaction_id, pay_client_type, pre_pay_id,
                     coupon_id, pay_time, target_user_id):
    '''
    添加支付流水、发起支付就会添加一条
    :param user_id:
    :param business_type:
    :param out_trade_no:
    :param total_fee:
    :param actual_fee:
    :param discount_amount:
    :param transaction_id:
    :param pay_client_type:
    :param pre_pay_id:
    :param coupon_id:
    :param pay_time:
    :param target_user_id: 目标用户ID，用于支付给其他用户
    :return:
    '''
    payment_flow = PaymentFlow(user_id, business_type, out_trade_no, total_fee, actual_fee, discount_amount, transaction_id,
                               pay_client_type, pre_pay_id, coupon_id, pay_time, target_user_id)
    db.session.add(payment_flow)
    db.session.flush()
    db.session.commit()
    return payment_flow.id


def pay_success(out_trade_no, transaction_id, actual_fee, pay_time, trade_type, response):
    '''
    支付成功更新数据
    :param out_trade_no:
    :param transaction_id:
    :param actual_fee:
    :param pay_time:
    :param trade_type:
    :param response:
    :return:
    '''
    payment_flow = PaymentFlow.query.filter_by(out_trade_no=out_trade_no).order_by(asc(PaymentFlow.create_time)).first()
    if payment_flow is None:
        raise BizException(ErrorCode.PAYMENT_FLOW_IS_NOT_EXIST)
    # 更新支付流水
    db.session.execute(update(PaymentFlow).where(PaymentFlow.id == payment_flow.id, PaymentFlow.status != 1).values(pay_status=1, status=1,
                                                                                           actual_fee=actual_fee,
                                                                                           transaction_id=transaction_id,
                                                                                           pay_time=pay_time,
                                                                                           pay_date=date_utils.convert_time_to_date_yyyymmdd_str(pay_time),
                                                                                           trade_type=trade_type,
                                                                                           response=response))


def get_payment_flow(out_trade_no):
    payment_flow = PaymentFlow.query.filter_by(out_trade_no=out_trade_no).order_by(asc(PaymentFlow.create_time)).first()
    if payment_flow is None:
        raise BizException(ErrorCode.PAYMENT_FLOW_IS_NOT_EXIST)
    return payment_flow




