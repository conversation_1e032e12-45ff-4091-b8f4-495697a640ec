from constans.system_config import SystemConfigEnum
from models.dataobject.user_data_object import UserDataObject
from models.explore_chat import ExploreChat
from models.explore_chat_log import ExploreChatLog
from models.user_session import UserSession
from models.user import User
from models.distributor import Distributor
from sso.session_user import SessionUser
from models.user_settings import UserSettings
from models.system_character import <PERSON>Character
from models.user_daily_usage import UserDailyUsage
from models.user_phone_account import UserPhoneAccount
from models.user_wechat_account import UserWechatAccount
from models.user_visitor_account import UserVisitorAccount
from models.user_account import UserAccount
from models.user_chat_log import UserChatLog
from models.user_oral_assessment_log import UserOralAssessmentLog
from models.user_chat import UserChat
from models.user_referrer import UserReferrer
from models.user_ielts_oral_course_log import UserIeltsOralCourseLog
from models.system_scene_topics import SystemSceneTopics
from models.community_scene_topics import CommunitySceneTopics
from models.user_ielts_info import UserIeltsInfo
from sqlalchemy import update, func, delete, or_
from sqlalchemy import asc
from sqlalchemy import desc
import json
from db import db
import time
from datetime import datetime
from cache import cache_service
from sso import authentication
from utils import aes_util
from utils import json_util
from flask_sqlalchemy_ext.flask_sqlalchemy_txns import transactional
from exception.biz_exception import BizException
from error_code import ErrorCode
from cache.models.user_settings_model import UserSettingsModel
from constans.english_levels import EnglishLevels
from constans.learning_purpose import LearningPurpose
from utils import code_gen_util
from utils import topic_util
from app_logconfig import error_logger
import config
from wechat import wechat_service
from oss import tencent_cos_service
from threading import Thread
from datetime import timedelta
from app_init import app
from models.user_sign_in import UserSignIn
from service import system_service


def get_user_info_by_pcode_or_phone(text):
    '''
    根据推广码获取用户信息
    :param text:
    :return:
    '''
    # promotion_code或者phone 等于 text
    user = User.query.filter((User.promotion_code == text) | (User.phone == text)).first()
    if user is None:
        raise BizException(ErrorCode.USER_IS_NOT_EXIST)
    return user

def get_user_info_by_keyworld(keyworld):
    '''
    根据推广码或者手机号获取用户信息
    :param promotion_code:
    :return:
    '''
    user = User.query.filter(or_(User.promotion_code==keyworld,User.phone==keyworld)).first()
    if user is None:
        raise BizException(ErrorCode.USER_IS_NOT_EXIST)
    return user

def add_user_total_tokens(user_id, total_tokens):
    '''
     更新日期为
    :param user_id: 用户ID
    :param date: 日期，例如 20230601
    :param total_tokens: 这次会话使用的tokens数量
    :return:
    '''

    # 获取当前日期
    current_date = datetime.now().date()

    # 将日期转换为 YYMMDD 格式的整数
    formatted_date = int(current_date.strftime('%Y%m%d'))

    user_daily_usage = UserDailyUsage.query.filter_by(user_id=user_id, date=formatted_date).first()

    if user_daily_usage is None:
        user_daily_usage = UserDailyUsage(user_id, formatted_date, total_tokens, 1, 0)
        db.session.add(user_daily_usage)
    else:
        db.session.execute(update(UserDailyUsage).where(UserDailyUsage.id == user_daily_usage.id,
                                                        UserDailyUsage.last_version == user_daily_usage.last_version).values(
            total_tokens=(user_daily_usage.total_tokens + total_tokens),
            num_of_call_api=(user_daily_usage.num_of_call_api + 1),
            last_version=(user_daily_usage.last_version + 1),
            update_time=time.time())
        )


def update_user_nick_name(user_id, nickname):
    '''
    设置用户的昵称，例如：Bruce
    :param user_id: 用户ID
    :param nick_name: 用户昵称，例如 Bruce
    :return:
    '''
    user = User.query.filter_by(id=user_id).first()
    if user is None:
        raise BizException(ErrorCode.USER_IS_NOT_EXIST)
    # 更新昵称
    db.session.execute(update(User).where(User.id == user_id).values(nickname=nickname))
    db.session.commit()


def update_user_phone(user_id, phone):
    '''
    设置用户的昵称，例如：Bruce
    :param user_id: 用户ID
    :param nick_name: 用户昵称，例如 Bruce
    :return:
    '''
    user = User.query.filter_by(id=user_id).first()
    if user is None:
        raise BizException(ErrorCode.USER_IS_NOT_EXIST)
    # 更新昵称
    db.session.execute(update(User).where(User.id == user_id).values(phone=phone))
    db.session.commit()


def get_session_user(access_token, token_key):
    '''
    :param access_token: 用户access_token
    :param token_key: token对应的key
    :return: session_user
    '''
    # 先从缓存取，取不到取数据库

    user_session = cache_service.get_user_session(token_key)
    if user_session is None:
        user_session = UserSession.query.filter_by(access_token=access_token, token_key=token_key).first()
        if user_session is not None:
            ext_map = user_session.ext_map
            session_user = json.loads(ext_map)
    else:
        return user_session
    if user_session is None:
        return None

    # logger.info('SessionUser object: %s', session_user)

    if session_user.get('openId') is not None and session_user.get('openId') != '':
        openid = session_user["openid"]
    else:
        openid = None

    if session_user.get('account_no') is not None and session_user.get('account_no') != '':
        account_no = session_user["account_no"]
    else:
        account_no = None

    if session_user.get('nickname') is not None and session_user.get('nickname') != '':
        nickname = session_user["nickname"]
    else:
        nickname = None
    if session_user.get('phone') is not None and session_user.get('phone') != '':
        phone = session_user["phone"]
    else:
        phone = None
    if session_user.get('session_key') is not None and session_user.get('session_key') != '':
        session_key = session_user["session_key"]
    else:
        session_key = None
    if session_user.get('register_time') is not None and session_user.get('register_time') != '':
        register_time = session_user["register_time"]
    else:
        register_time = None

    # 统一返回为SessionUser对象
    session_user = SessionUser(session_user["user_id"], account_no, nickname, session_user["access_token"], openid, session_key, phone, register_time)
    return session_user


def add_user_chat_log(user_id, conversation_id, ai_message, user_message, total_tokens, completion_tokens, gpt_rt,
                      audio_rt, vedio_rt, total_rt, topic, client_sentence_id,
                      user_audio_url, ai_audio_url):
    """
    添加用户聊天记录
    :param user_id: 用户ID
    :param conversation_id: 对话ID
    :param ai_message: AI回复的消息
    :param user_message:用户的消息
    :param total_tokens:一次对话消耗的tokens数量
    :param completion_tokens:一次对话消耗的tokens数量
    :param gpt_rt:
    :param audio_rt
    :param vedio_rt
    :param total_rt
    :param topic
    :param client_sentence_id
    :param user_audio_url user_audio_url
    :param ai_audio_url ai_audio_url
    :return:
    """
    # 获取当前日期
    current_date = datetime.now().date()

    # 将日期转换为 YYMMDD 格式的整数
    formatted_date = int(current_date.strftime('%Y%m%d'))

    user_chat_log = UserChatLog(user_id, formatted_date, conversation_id, ai_message, user_message, total_tokens,
                                completion_tokens, gpt_rt, audio_rt, vedio_rt, total_rt, topic, client_sentence_id,
                                user_audio_url, ai_audio_url)
    db.session.add(user_chat_log)


def add_explore_chat_log(user_id, conversation_id, ai_message, user_message, total_tokens, completion_tokens, gpt_rt,
                      audio_rt, vedio_rt, total_rt):
    '''

    :param user_id:
    :param conversation_id:
    :param ai_message:
    :param user_message:
    :param total_tokens:
    :param completion_tokens:
    :param gpt_rt:
    :param audio_rt:
    :param vedio_rt:
    :param total_rt:
    :param ai_audio_url:
    :return:
    '''
    # 获取当前日期
    current_date = datetime.now().date()

    # 将日期转换为 YYMMDD 格式的整数
    formatted_date = int(current_date.strftime('%Y%m%d'))

    explore_chat_log = ExploreChatLog(user_id, formatted_date, conversation_id, ai_message, user_message, total_tokens,
                                completion_tokens, gpt_rt, audio_rt, vedio_rt, total_rt)
    db.session.add(explore_chat_log)


# 当前轮数是否大于等于1
def is_current_round_greater_than_or_equal_to_one(user_id, conversation_id):
    '''
    判断当前轮数是否大于等于1
    :param user_id:
    :param conversation_id:
    :return:
    '''
    # 先检查缓存

    user_chat = UserChat.query.filter_by(user_id=user_id, conversation_id=conversation_id).first()
    if user_chat is not None:
        return user_chat.current_round >= 1
    return False



def get_current_round(user_id, conversation_id):
    '''
    获取用户对话的当前轮数
    :param user_id:
    :param conversation_id:
    :return:
    '''
    # 先从缓存取，取不到取数据库
    current_round = cache_service.get_conversation_round(user_id, conversation_id)
    if current_round is not None:
        return current_round
    user_chat = UserChat.query.filter_by(user_id=user_id, conversation_id=conversation_id).first()
    if user_chat is not None:
        return user_chat.current_round
    return 0


def plus_user_chat_current_round(user_id, conversation_id):
    '''
    增加用户对话的当前轮数
    :param user_id:
    :param conversation_id:
    :return:
    '''
    user_chat = UserChat.query.filter_by(user_id=user_id, conversation_id=conversation_id).first()
    current_round = user_chat.current_round + 1
    if user_chat is not None:
        db.session.execute(update(UserChat).where(UserChat.id == user_chat.id).values(
            current_round=current_round,
            update_time=time.time())
        )
    cache_service.put_conversation_round(user_id, conversation_id, current_round)


def update_available_minutes_for_chat(user_id, conversation_id):
    '''
    更新用户可用时长
    :param user_id:
    :param conversation_id:
    :return:
    '''
    result = UserChatLog.query.filter_by(user_id=user_id, conversation_id=conversation_id).order_by(desc(UserChatLog.create_time)).limit(2).all()
    if len(result) <= 1:
        return

    # 获取第一个UserChatLog对象
    first_user_chat_log = result[0]

    # 获取第二个UserChatLog对象
    second_user_chat_log = result[1]

    used_minutes = (first_user_chat_log.create_time - second_user_chat_log.create_time);

    used_minutes = round(used_minutes / 60, 2)

    user = User.query.filter_by(id=user_id).first()
    if user is not None:
        available_minutes = round(user.available_minutes - used_minutes, 2)
        # 如果可用时长小于0，则设置为0
        if available_minutes < 0:
            available_minutes = 0
        # 判断如果不是会员，则扣除可用时长
        if user.member_type == 0:
            db.session.execute(update(User).where(User.id == user_id).values(
                available_minutes=available_minutes,
                used_minutes=(round(user.used_minutes + used_minutes, 2)),
                update_time=time.time())
            )
        # 判断如果是会员，但是会员到期时间小于当前时间，则扣除可用时长
        elif (user.member_type == 1 and user.expiration_time <= datetime.now()) :
            db.session.execute(update(User).where(User.id == user_id).values(
                available_minutes=available_minutes,
                used_minutes=(round(user.used_minutes + used_minutes, 2)),
                member_type=0,
                update_time=time.time())
            )
        # 判断如果是会员且会员到期时间大于当前时间，则不扣除可用时长，只更新used_minutes
        else:
            # 如果是会员且会员到期时间大于当前时间，则不扣除可用时长
            db.session.execute(update(User).where(User.id == user_id).values(
                used_minutes=(round(user.used_minutes + used_minutes, 2)),
                update_time=time.time())
            )
        


def update_available_minutes_for_explore_chat(user_id, conversation_id):
    result = ExploreChatLog.query.filter_by(user_id=user_id, conversation_id=conversation_id).order_by(desc(ExploreChatLog.create_time)).limit(2).all()
    if len(result) <= 1:
        return

    # 获取第一个UserChatLog对象
    first_user_chat_log = result[0]

    # 获取第二个UserChatLog对象
    second_user_chat_log = result[1]

    used_minutes = (first_user_chat_log.create_time - second_user_chat_log.create_time);

    used_minutes = round(used_minutes / 60, 2)

    user = User.query.filter_by(id=user_id).first()
    if user is not None:
        available_minutes = round(user.available_minutes - used_minutes, 2)
        # 如果可用时长小于0，则设置为0
        if available_minutes < 0:
            available_minutes = 0
        # 判断如果不是会员或者是会员但是会员到期时间小于当前时间，则扣除可用时长
        if user.member_type == 0:
            db.session.execute(update(User).where(User.id == user_id).values(
                available_minutes=available_minutes,
                used_minutes=(round(user.used_minutes + used_minutes, 2)),
                update_time=time.time())
            )
        elif (user.member_type == 1 and user.expiration_time <= datetime.now()) :
            db.session.execute(update(User).where(User.id == user_id).values(
                available_minutes=available_minutes,
                used_minutes=(round(user.used_minutes + used_minutes, 2)),
                member_type=0,
                update_time=time.time())
            )
        else:
            # 如果是会员且会员到期时间大于当前时间，则不扣除可用时长
            db.session.execute(update(User).where(User.id == user_id).values(
                used_minutes=(round(user.used_minutes + used_minutes, 2)),
                update_time=time.time())
            )


def get_user_oral_assessment_log(user_id, conversation_id):
    '''
    根据用户ID和对话ID获取UserOralAssessmentLog列表数据
    :param user_id:
    :param conversation_id:
    :return:
    '''
    query = UserOralAssessmentLog.query.filter_by(user_id=user_id, conversation_id=conversation_id).order_by(asc(UserOralAssessmentLog.create_time))
    return query.all()


def get_ielts_oral_course_log(user_id, conversation_id):
    '''
    根据用户ID和对话ID获取UserChatLog列表数据
    :param user_id:
    :param conversation_id:
    :return:
    '''
    query = UserIeltsOralCourseLog.query.filter_by(user_id=user_id, conversation_id=conversation_id).order_by(asc(UserIeltsOralCourseLog.create_time))
    return query.all()


def update_available_minutes_for_ielts_oral_test(user_id, conversation_id):
    '''
    更新用户雅思口语测试的可用时长
    :param user_id:
    :param conversation_id:
    :return:
    '''
    result = UserOralAssessmentLog.query.filter_by(user_id=user_id, conversation_id=conversation_id).order_by(desc(UserOralAssessmentLog.create_time)).limit(2).all()
    if len(result) <= 1:
        return

    # 获取第一个UserChatLog对象
    first_user_chat_log = result[0]

    # 获取第二个UserChatLog对象
    second_user_chat_log = result[1]

    used_minutes = (first_user_chat_log.create_time - second_user_chat_log.create_time);

    used_minutes = round(used_minutes / 60, 2)

    user = User.query.filter_by(id=user_id).first()
    if user is not None:
        available_minutes = round(user.available_minutes - used_minutes, 2)
        # 如果可用时长小于0，则设置为0
        if available_minutes < 0:
            available_minutes = 0
        # 判断如果不是会员或者是会员但是会员到期时间小于当前时间，则扣除可用时长
        if user.member_type == 0 or (user.member_type == 1 and user.expiration_time <= datetime.now()):
            db.session.execute(update(User).where(User.id == user_id).values(
                available_minutes=available_minutes,
                used_minutes=(round(user.used_minutes + used_minutes, 2)),
                update_time=time.time())
            )
        elif (user.member_type == 1 and user.expiration_time <= datetime.now()) :
            db.session.execute(update(User).where(User.id == user_id).values(
                available_minutes=available_minutes,
                used_minutes=(round(user.used_minutes + used_minutes, 2)),
                member_type=0,
                update_time=time.time())
            )
        else:
            # 如果是会员且会员到期时间大于当前时间，则不扣除可用时长
            db.session.execute(update(User).where(User.id == user_id).values(
                used_minutes=(round(user.used_minutes + used_minutes, 2)),
                update_time=time.time())
            )


def update_available_minutes_for_ielts_oral_course(user_id, conversation_id):
    '''
    更新用户雅思口语课程的可用时长
    :param user_id:
    :param conversation_id:
    :return:
    '''
    result = UserIeltsOralCourseLog.query.filter_by(user_id=user_id, conversation_id=conversation_id).order_by(desc(UserIeltsOralCourseLog.create_time)).limit(2).all()
    if len(result) <= 1:
        return

    # 获取第一个UserChatLog对象
    first_user_chat_log = result[0]

    # 获取第二个UserChatLog对象
    second_user_chat_log = result[1]

    used_minutes = (first_user_chat_log.create_time - second_user_chat_log.create_time);

    used_minutes = round(used_minutes / 60, 2)

    user = User.query.filter_by(id=user_id).first()
    if user is not None:
        available_minutes = round(user.available_minutes - used_minutes, 2)
        # 如果可用时长小于0，则设置为0
        if available_minutes < 0:
            available_minutes = 0
        # 判断如果不是会员或者是会员但是会员到期时间小于当前时间，则扣除可用时长
        if user.member_type == 0 or (user.member_type == 1 and user.expiration_time <= datetime.now()):
            db.session.execute(update(User).where(User.id == user_id).values(
                available_minutes=available_minutes,
                used_minutes=(round(user.used_minutes + used_minutes, 2)),
                update_time=time.time())
            )
        elif (user.member_type == 1 and user.expiration_time <= datetime.now()) :
            db.session.execute(update(User).where(User.id == user_id).values(
                available_minutes=available_minutes,
                used_minutes=(round(user.used_minutes + used_minutes, 2)),
                member_type=0,
                update_time=time.time())
            )
        else:
            # 如果是会员且会员到期时间大于当前时间，则不扣除可用时长
            db.session.execute(update(User).where(User.id == user_id).values(
                used_minutes=(round(user.used_minutes + used_minutes, 2)),
                update_time=time.time())
            )


def get_user_chat_log(user_id, conversation_id):
    '''
    根据用户ID和对话ID获取对话列表数据
    :param user_id:
    :param conversation_id:
    :return:
    '''
    query = UserChatLog.query.filter_by(user_id=user_id, conversation_id=conversation_id).order_by(asc(UserChatLog.create_time))
    return query.all()


def get_explore_chat_log(user_id, conversation_id):
    '''
    根据用户ID和对话ID获取对话列表数据
    :param user_id:
    :param conversation_id:
    :return:
    '''
    query = ExploreChatLog.query.filter_by(user_id=user_id, conversation_id=conversation_id).order_by(asc(ExploreChatLog.create_time))
    return query.all()


def get_user_chat(user_id, conversation_id):
    '''
    根据用户ID和对话ID获取对话列表数据
    :param user_id:
    :param conversation_id:
    :return:
    '''
    return UserChat.query.filter_by(user_id=user_id, conversation_id=conversation_id).first()


def add_user_chat(user_id, conversation_id, topic_code, mode):
    """
    添加用户聊天
    :param user_id: 用户ID
    :param conversation_id:对话ID，表示一次对话
    :param topic_code: 话题编码
    :return:
    """
    user_chat = UserChat.query.filter_by(user_id=user_id, conversation_id=conversation_id).first()
    if user_chat is not None:
        return

    # 如果topic为系统主题
    if not topic_util.is_community_topic(topic_code):
        system_scene_topics = SystemSceneTopics.query.filter_by(topic_code=topic_code).first()
        if system_scene_topics is not None:
            topic_name = system_scene_topics.chinese
        else:
            topic_name = ""
    else:
        topic = CommunitySceneTopics.query.filter_by(topic_code=topic_code).first()
        if topic is not None:
            topic_name = topic.chinese
        else:
            topic_name = ""

    # 获取当前日期
    current_date = datetime.now().date()

    # 将日期转换为 YYMMDD 格式的整数
    formatted_date = int(current_date.strftime('%Y%m%d'))

    user_chat = UserChat(user_id, formatted_date, conversation_id, topic_code, topic_name, mode)
    db.session.add(user_chat)


def add_explore_chat(user_id, conversation_id, explore_type, topic_code):
    """
    添加用户聊天
    :param user_id: 用户ID
    :param conversation_id:对话ID，表示一次对话
    :param topic_code: 话题编码
    :return:
    """
    explore_chat = ExploreChat.query.filter_by(user_id=user_id, conversation_id=conversation_id).first()
    if explore_chat is not None:
        return

    # 获取当前日期
    current_date = datetime.now().date()

    # 将日期转换为 YYMMDD 格式的整数
    formatted_date = int(current_date.strftime('%Y%m%d'))

    explore_chat = ExploreChat(user_id, formatted_date, conversation_id, explore_type, topic_code)
    db.session.add(explore_chat)


def save_or_update(app_key, uid, uniq_no, access_token, token_key, session_user):
    user_session = UserSession.query.filter_by(token_key=token_key).first()

    # 如果不存在则插入
    if user_session is None:
        user_session = UserSession(app_key, uid, uniq_no, access_token, token_key,
                                   json.dumps(session_user.__dict__))
        db.session.add(user_session)
    else:
        # db.session.query(UserSession).filter_by(id=user_session.id).update(uniq_no=uniq_no, access_token=access_token,
        #                                                                    token_key=token_key,
        #                                                                    ext_map=json.dumps(session_user.__dict__),
        #                                                                    update_time=time.time())

        db.session.execute(update(UserSession).where(UserSession.id == user_session.id).values(uniq_no=uniq_no,
                                                                                               access_token=access_token,
                                                                                               token_key=token_key,
                                                                                               ext_map=json.dumps(
                                                                                                   session_user.__dict__, ensure_ascii=False),
                                                                                               update_time=time.time()))
    db.session.commit()


def save_or_update_user_character_id(user_id, character_id):
    user_settings = UserSettings.query.filter_by(user_id=user_id).first()
    system_character = SystemCharacter.query.filter_by(id=character_id).first()
    # 如果不存在则插入
    if user_settings is None:
        user_settings = UserSettings(user_id, character_id, 1, None, 1, None)
        db.session.add(user_settings)
        db.session.commit()
        # 将该人物的使用人数加1
        if system_character is not None:
            db.session.execute(update(SystemCharacter).where(SystemCharacter.id == system_character.id).values(
                number_of_users=(system_character.number_of_users + 1)))
            db.session.commit()
        # 设置user_settings缓存
            system_character = SystemCharacter.query.filter_by(id=user_settings.character_id).first()
            if config.TTS_PROVIDER == 'openai_tts':
                voice = system_character.openai_tts_voice
            else:
                voice = system_character.voice

            user_settings_model =  UserSettingsModel(user_id, user_settings.playback_speed,
                                                     system_character.name,system_character.age, system_character.age_group, system_character.gender,
                                                     voice, system_character.english_variation,
                                                     system_character.character_type, system_character.self_introduction, system_character.static_video_url,
                                                     system_character.greeting_video_url, system_character.greeting,
                                                     system_character.greeting_cn, system_character.image_url, system_character.audio_url,
                                                     system_character.conversation_video_url, user_settings.is_video_open,
                                                     system_character.icon_url, system_character.chat_character_url, system_character.mbti,
                                                     system_character.home_page_bg_image_url, user_settings.learning_phase, user_settings.is_mixed_teaching,
                                                     json_util.load_json_with_fix(user_settings.learning_purpose))
            cache_service.put_user_settings(user_id, user_settings_model)
    else:

        # 如果character_id跟之前的不一样，则将之前的人物的使用人数减1，将新的人物的使用人数加1
        if system_character is not None and user_settings.character_id != character_id:
            db.session.execute(update(SystemCharacter).where(SystemCharacter.id == system_character.id).values(
                number_of_users=(system_character.number_of_users + 1)))
            db.session.commit()
            old_system_character = SystemCharacter.query.filter_by(id=user_settings.character_id).first()
            if old_system_character is not None:
                db.session.execute(update(SystemCharacter).where(SystemCharacter.id == old_system_character.id).values(
                    number_of_users=(old_system_character.number_of_users - 1)))
                db.session.commit()

        db.session.execute(update(UserSettings).where(UserSettings.id == user_settings.id).values(character_id=character_id,
                                                                                                          update_time=time.time()))
        db.session.commit()
        cache_service.delete_user_setting(user_id)
    return character_id


def save_or_update_user_playback_speed(user_id, playback_speed):
    user_settings = UserSettings.query.filter_by(user_id=user_id).first()

    # 如果不存在则插入
    if user_settings is None:
        user_settings = UserSettings(user_id, 1, playback_speed, None, 1, None)
        db.session.add(user_settings)
    else:
        db.session.execute(update(UserSettings).where(UserSettings.id == user_settings.id).values(playback_speed=playback_speed,
                                                                                              update_time=time.time()))
        cache_service.delete_user_setting(user_id)
    db.session.commit()
    return playback_speed


def save_or_update_user_video_open(user_id, is_video_open):
    user_settings = UserSettings.query.filter_by(user_id=user_id).first()

    # 如果不存在则插入
    if user_settings is None:
        user_settings = UserSettings(user_id, 1, 1, None, 1, None)
        db.session.add(user_settings)
    else:
        db.session.execute(update(UserSettings).where(UserSettings.id == user_settings.id).values(is_video_open=is_video_open,
                                                                                                  update_time=time.time()))
        cache_service.delete_user_setting(user_id)
    db.session.commit()
    return is_video_open


def delete_user_settings(user_id):
    cache_service.delete_user_setting(user_id)




def delete_user(user_id, token_key):
    # delete user cache
    cache_service.delete_user(user_id)
    cache_service.delete_user_setting(user_id)
    cache_service.delete_user_session(token_key)
    # 删除用户 User,UserPhoneAccount,UserAccount,UserWechatAccount,UserSession
    db.session.execute(delete(User).where(User.id == user_id))
    db.session.execute(delete(UserPhoneAccount).where(UserPhoneAccount.user_id == user_id))
    db.session.execute(delete(UserAccount).where(UserAccount.user_id == user_id))
    db.session.execute(delete(UserWechatAccount).where(UserWechatAccount.user_id == user_id))
    db.session.execute(delete(UserSession).where(UserSession.uid == user_id))
    db.session.commit()

def get_user_settings_for_asy(user_id):
    with app.app_context():
        return get_user_settings(user_id)


def get_user_settings(user_id):
    user_settings_model = cache_service.get_user_settings(user_id)
    if user_settings_model is None:
        user_settings = UserSettings.query.filter_by(user_id=user_id).first()
        if user_settings is not None:
            system_character = SystemCharacter.query.filter_by(id=user_settings.character_id).first()
            # if config.TTS_PROVIDER == 'openai_tts':
            #     voice = system_character.openai_tts_voice
            # else:

            voice = system_character.voice
            user_settings_model =  UserSettingsModel(user_id, user_settings.playback_speed,
                                                     system_character.name,system_character.age, system_character.age_group, system_character.gender,
                                                     voice, system_character.english_variation,
                                                     system_character.character_type, system_character.self_introduction, system_character.static_video_url,
                                                     system_character.greeting_video_url, system_character.greeting,
                                                     system_character.greeting_cn, system_character.image_url, system_character.audio_url,
                                                     system_character.conversation_video_url, user_settings.is_video_open, system_character.icon_url, 
                                                     system_character.chat_character_url, system_character.mbti, system_character.home_page_bg_image_url,
                                                     user_settings.learning_phase, user_settings.is_mixed_teaching, 
                                                     json_util.load_json_with_fix(user_settings.learning_purpose))
            cache_service.put_user_settings(user_id, user_settings_model)
        else:
                        # 保存用户默认设置

            system_character = SystemCharacter.query.filter_by(id=1).first()

            user_settings = UserSettings(user_id, system_character.id, 1, None, 1, None)
            db.session.add(user_settings)
            # if config.TTS_PROVIDER == 'openai_tts':
            #     voice = system_character.openai_tts_voice
            # else:
            voice = system_character.voice
            user_settings_model = UserSettingsModel(user_id, 1,
                                                     system_character.name,system_character.age, system_character.age_group, system_character.gender,
                                                     voice, system_character.english_variation,
                                                     system_character.character_type, system_character.self_introduction, system_character.static_video_url,
                                                     system_character.greeting_video_url, system_character.greeting,
                                                     system_character.greeting_cn, system_character.image_url, system_character.audio_url,
                                                     system_character.conversation_video_url, 0, system_character.icon_url, 
                                                     system_character.chat_character_url, system_character.mbti, system_character.home_page_bg_image_url,
                                                     user_settings.learning_phase, user_settings.is_mixed_teaching, 
                                                     json_util.load_json_with_fix(user_settings.learning_purpose))

            # 人物使用人数加1
            db.session.execute(update(SystemCharacter).where(SystemCharacter.id == system_character.id).values(
                number_of_users=(system_character.number_of_users + 1)))
            db.session.commit()

            cache_service.put_user_settings(user_id, user_settings_model)
    '''
    存量数据兼容处理、重新设置缓存
    '''
    # if config.TTS_PROVIDER == 'openai_tts' and user_settings_model.character_voice != 'nova' and user_settings_model.character_voice != 'shimmer'\
    #         and user_settings_model.character_voice != 'echo' and user_settings_model.character_voice != 'fable':
    #     system_character = SystemCharacter.query.filter_by(id=1).first()
    #     if config.TTS_PROVIDER == 'openai_tts':
    #         voice = system_character.openai_tts_voice
    #     else:
    #         voice = system_character.voice
    #     user_settings_model =  UserSettingsModel(user_id, 1,
    #                                              system_character.name,system_character.age, system_character.age_group, system_character.gender,
    #                                              voice, system_character.english_variation,
    #                                              system_character.character_type, system_character.self_introduction, system_character.static_video_url,
    #                                              system_character.greeting_video_url, system_character.greeting,
    #                                              system_character.greeting_cn, system_character.image_url, system_character.audio_url,
    #                                              system_character.conversation_video_url, 0, system_character.icon_url, system_character.chat_character_url)
    #     cache_service.put_user_settings(user_id, user_settings_model)
    return user_settings_model



def get_promotion_qrcode_url(promotion_code, env):
    '''
    获取推广二维码:
    1、先用promotion_code和env作为参数调用get_unlimited_qrcode方法获取图片二进制数据。
    2、将图片二进制数据上传到腾讯云cos，获取到图片的url
    :param promotion_code:
    :param env:
    :return:
    '''
    # 获取不限制的小程序码
    qrcode_data = wechat_service.get_unlimited_qrcode(promotion_code, env)
    # 将二进制数据qrcode_data写入到本地文件
    file_name = config.USER_UPLOAD_FILE_DIR_PATH + '/' + promotion_code + '.jpg'
    with open(file_name, 'wb') as f:
        f.write(qrcode_data)
    # 将本地文件上传到腾讯云cos
    key = promotion_code + '.jpg'
    dir = 'user_assets/promotion_qrcode'
    tencent_cos_service.upload_thread(file_name, dir, key)
    return 'https://xinquai-**********.cos.ap-guangzhou.myqcloud.com/'+dir +'/'+key



@transactional
def create_user_and_user_phone_account(phone, referral_code, verification_code):
    p_code = get_p_code()
    # 根据手机号查询用户，如果不存在则创建用户
    user = User.query.filter_by(phone=phone).first()
    # 用户已经用手机号登录过或者登录过微信然后授权过手机号
    if user is not None:
        user_phone_account = UserPhoneAccount(user.id, phone, verification_code)
        db.session.add(user_phone_account)
    # 用户第一次登录或者微信登录过但是没有授权过手机号
    else:
        user = User("Taylor", phone, referral_code, None, None, p_code, get_account_no(), 0)
        db.session.add(user)
        db.session.flush()
        user_phone_account = UserPhoneAccount(user.id, phone, verification_code)
        db.session.add(user_phone_account)
    # 异步更新推广二维码
    update_promotion_qrcode_url_thread(user.id, p_code)
    return user


def create_user(phone, referral_code):
    """
    创建用户(有些场景只需要创建用户：例如邀请用户成为推广员（用户没登录的情况下）)
    :param phone:
    :param referral_code:
    :return:
    """
    user = User.query.filter_by(phone=phone).first()
    if user is None:
        p_code = get_p_code()
        user = User("Taylor", phone, referral_code, None, None, p_code, get_account_no(), 0)
        db.session.add(user)
        db.session.flush()
        update_promotion_qrcode_url_thread(user.id, p_code)
    return user


def get_p_code():
    """
    获取推广码
    :return:
    """
    while True:
        p_code = code_gen_util.generate_promotion_code()
        user = User.query.filter_by(promotion_code=p_code).first()
        if user is None:
            return p_code


def get_account_no():
    """
    获取账号
    :return:
    """
    while True:
        account_no = code_gen_util.generate_random_account()
        user = User.query.filter_by(account_no=account_no).first()
        if user is None:
            return account_no

@transactional
def create_user_and_user_wechat_account(openid):
    p_code = get_p_code()
    user = User('Jack', '', '', None,  None, p_code, get_account_no(), 0)
    db.session.add(user)
    db.session.flush()
    user_wechat_account = UserWechatAccount(user.id, openid)
    db.session.add(user_wechat_account)
    update_promotion_qrcode_url_thread(user.id, p_code)
    return user.id

@transactional
def create_user_and_user_visitor_account(device_id,os_type):
    p_code = get_p_code()
    user = User('Visitor', '', '', None,  None, p_code, get_account_no(), 1)
    db.session.add(user)
    db.session.flush()
    user_visitor_account = UserVisitorAccount(user.id, device_id, os_type)
    db.session.add(user_visitor_account)
    update_promotion_qrcode_url_thread(user.id, p_code)
    return user.id

def update_promotion_qrcode_url_thread(user_id, promotion_code):
    '''
    启动线程异步执行 update_promotion_qrcode_url方法
    :param user_id:
    :param promotion_code:
    :return:
    '''
    t = Thread(target=update_promotion_qrcode_url_for_thread, args=(user_id, promotion_code))
    t.start()


def update_promotion_qrcode_url_for_thread(user_id, p_code):
    with app.app_context():
        env = config.ENV
        promotion_qrcode_url = get_promotion_qrcode_url(p_code, env)
        db.session.execute(update(User).where(User.id == user_id).values(promotion_qrcode_url=promotion_qrcode_url,
                                                                        update_time=time.time()))
        # 创建分销员distributor
        distributor = Distributor.query.filter_by(user_id=user_id).first()
        if distributor is None:
            # 初始化一条分销员数据，为了后续可以分佣的计算
            distributor = Distributor('', '', user_id, '', '', 0, '', 1)
            db.session.add(distributor)
        db.session.commit()


@transactional
def create_user_and_user_account(username, password, nick_name, phone, referral_code):
    p_code = get_p_code()
    user = User(nick_name, phone, referral_code, None, None, p_code, get_account_no(), 0)
    # save the object into the database
    db.session.add(user)
    db.session.flush()
    # print('user_id:' + str(user.id))
    # 异步更新推广二维码
    update_promotion_qrcode_url_thread(user.id, p_code)

    user_account = UserAccount(user.id, username, aes_util.md5_hash(password))
    db.session.add(user_account)
    db.session.flush()
    return user


def grant_session_user(user_id, app_key, uniq_no):
    # 获取到用户表数据
    user = User.query.get(user_id)
    # 如果正确则创建token
    access_token = authentication.create_token(app_key, user.id)
    # 放入redis中的key
    redis_token_key = "SSOT" + ":" + app_key + str(user_id)
    # print("grant_session_user_phone______" + user.phone)
    session_user = SessionUser(user_id, user.account_no, user.nickname, access_token, None, None, user.phone, user.register_time)
    # 保存到缓存
    cache_service.put_user_session(redis_token_key, session_user)
    # uniq_no 为 客户端唯一编号
    # save_or_update user_session
    save_or_update(app_key, user.id, uniq_no, access_token, redis_token_key, session_user)
    return session_user


def grant_session_user_for_weixin(user_id, app_key, uniq_no, openid, session_key):
    # 获取到用户表数据
    user = User.query.get(user_id)
    user_data_object = UserDataObject.from_user(user)
    # 将user缓存到缓存
    cache_service.put_user(user_id, user_data_object)
    # 如果正确则创建token
    access_token = authentication.create_token(app_key, user.id)
    # 放入redis中的key
    redis_token_key = "SSOT" + ":" + app_key + str(user_id)
    session_user = SessionUser(user_id, user.account_no, user.nickname, access_token, openid, session_key, user.phone, user.register_time)
    # 保存到缓存
    cache_service.put_user_session(redis_token_key, session_user)
    # uniq_no 为 客户端唯一编号
    # save_or_update user_session
    save_or_update(app_key, user.id, uniq_no, access_token, redis_token_key, session_user)
    return session_user


def get_days_of_study(register_time):
    '''
    根据注册时间计算学习的天数
    :param register_time:
    :return:
    '''
    now_time = time.time()    # 假设这是结束时间的时间戳

    # 计算秒数差
    seconds_diff = now_time - register_time

    # 将秒数差转换为天数差
    days_diff = seconds_diff / (60 * 60 * 24)

    return int(days_diff+1)

'''
1、Beginner Level:

Basic vocabulary and simple sentence structures.
Can understand and use common greetings, introductions, and basic expressions.
Limited ability to engage in simple conversations.

2、Elementary Level:

Expanded vocabulary with more varied sentence structures.
Can handle everyday situations like shopping, ordering food, and asking for directions.
Able to hold short conversations on familiar topics.

3、Pre-Intermediate Level:

Improved vocabulary and sentence complexity.
Can participate in conversations related to personal interests and experiences.
Can understand the main points of simple texts and discussions.

4、Intermediate Level:

Good command of general vocabulary and sentence structures.
Can express opinions and discuss a wider range of topics.
Can understand and participate in more complex conversations and discussions.

5、Upper Intermediate Level:

Advanced vocabulary and ability to use more complex sentence structures.
Can follow and contribute to discussions on abstract topics.
Can understand and summarize more challenging texts.

6、Advanced Level:

Extensive vocabulary and strong command of various language features.
Can communicate fluently and effectively in a wide range of contexts.
Can understand and analyze complex texts, including academic or technical materials.

7、Proficient Level:

Near-native fluency and excellent command of the language.
Can engage in nuanced and sophisticated conversations.
Can understand and critically analyze complex texts and discussions.

8、Native Level:

Native-like fluency and mastery of the language.
Can communicate effortlessly in all contexts, including highly specialized ones.
Able to understand and produce language at a level comparable to native speakers.



1、初学者水平 (Beginner Level)：

基础词汇和简单的句子结构。
能理解和使用常见的问候语、介绍和基本表达。
能够在简单对话中有限的参与能力。

2、初级水平 (Elementary Level)：

扩展的词汇和更多变化的句子结构。
能够处理购物、点餐和问路等日常情境。
能够进行有关熟悉话题的简短对话。

3、预中级水平 (Pre-Intermediate Level)：

提升的词汇量和句子复杂性。
能够参与与个人兴趣和经历相关的对话。
能够理解简单文本和讨论的主要要点。

4、中级水平 (Intermediate Level)：

掌握了一般词汇和句子结构。
能够表达意见并讨论更广泛的话题。
能够理解和参与更复杂的对话和讨论。

5、高级中级水平 (Upper Intermediate Level)：

高级词汇和使用更复杂句子结构的能力。
能够跟随和参与抽象话题的讨论。
能够理解并总结更具挑战性的文本。

6、高级水平 (Advanced Level)：

丰富的词汇量和各种语言特点的熟练运用。
能够在广泛的语境中流利、有效地交流。
能够理解和分析复杂的文本，包括学术或技术材料。

7、精通水平 (Proficient Level)：

接近母语水平的流利程度和优秀的语言掌握能力。
能够在各种语境下进行微妙而复杂的对话。
能够理解并批判性地分析复杂的文本和讨论。

8、母语水平 (Native Level)：

像母语者一样流利，掌握了语言的精髓。
在所有语境下都能毫不费力地交流，包括高度专业化的领域。
能够理解和产生与母语者相媲美的语言表达。
'''

def get_all_english_levels():
    # dict_list = [
    #     {"english_level": "Beginner Level", "chinese": "初学者水平", "chinese_abbreviation": "初学者", "description": "能够理解基础词汇和简单的句子结构。"},
    #     {"english_level": "Elementary Level", "chinese": "初级水平", "chinese_abbreviation": "初级", "description": "能够处理购物、点餐和问路等日常情境。"},
    #     {"english_level": "Intermediate Level", "chinese": "中级水平", "chinese_abbreviation": "中级", "description": "能够表达意见并讨论更广泛的话题。"},
    #     {"english_level": "Advanced Level", "chinese": "高级水平", "chinese_abbreviation": "高级", "description": "能够在广泛的语境中流利、有效地交流,包括学术或技术材料。"}
    # ]
    # return dict_list
    # 转换为字典列表
    dict_list = [level.to_dict() for level in EnglishLevels]
    return dict_list


def set_english_level(user_id, english_level):

    '''
    设置用户的昵称，例如：Bruce
    :param user_id: 用户ID
    :param english_level: 英语等级，例如：Beginner Level
    :return:
    '''
    user = User.query.filter_by(id=user_id).first()
    if user is None:
        raise BizException(ErrorCode.USER_IS_NOT_EXIST)
    # 更新昵称
    db.session.execute(update(User).where(User.id == user_id).values(english_level=english_level))
    db.session.commit()
    cache_service.delete_user(user_id)


def set_avatar_url(user_id, avatar_url):

    '''
    设置用户的头像
    :param user_id: 用户ID
    :param avatar_url: 用户头像
    :return:
    '''
    user = User.query.filter_by(id=user_id).first()
    if user is None:
        raise BizException(ErrorCode.USER_IS_NOT_EXIST)
    # 更新昵称
    db.session.execute(update(User).where(User.id == user_id).values(avatar_url=avatar_url))
    db.session.commit()
    cache_service.delete_user(user_id)


def set_user_sex(user_id, sex):
    '''
    设置用户的性别
    :param user_id: 用户ID
    :param
    :return:
    '''
    user = User.query.filter_by(id=user_id).first()
    if user is None:
        raise BizException(ErrorCode.USER_IS_NOT_EXIST)
    # 更新用户性别
    db.session.execute(update(User).where(User.id == user_id).values(sex=sex))
    db.session.commit()
    cache_service.delete_user(user_id)


def get_all_learning_purposes():
    '''
    获取所有学习目的
    '''
    # 转换为字典列表
    dict_list = [level.to_dict() for level in LearningPurpose]
    return dict_list


def set_learning_purpose(user_id, learning_purpose):
    '''
    设置用户的学习目的，例如：Bruce
    :param user_id: 用户ID
    :param learning_purpose: 学习目的，例如：student_study
    :return:
    '''
    user = User.query.filter_by(id=user_id).first()
    if user is None:
        raise BizException(ErrorCode.USER_IS_NOT_EXIST)
    # 更新学习目的
    # learning_purpose 是一个JSON数组格式字符串，例如：["student_study", "travel"]
    # learning_purpose_json = json.dumps(learning_purpose, ensure_ascii=False)
    # db.session.execute(update(User).where(User.id == user_id).values(learning_purpose=learning_purpose_json))
    # db.session.commit()
    # cache_service.delete_user(user_id)

    #更新user_settings表中的learning_purpose字段
    learning_purpose_json = json.dumps(learning_purpose, ensure_ascii=False)
    db.session.execute(update(UserSettings).where(UserSettings.user_id == user_id).values(learning_purpose=learning_purpose_json))
    db.session.commit()
    cache_service.delete_user_setting(user_id)

def get_user(user_id):
    user_data_object = cache_service.get_user(user_id)
    if user_data_object is None:
        user = User.query.filter_by(id=user_id).first()
        if user is None:
            raise BizException(ErrorCode.USER_IS_NOT_EXIST)
        user_data_object = UserDataObject.from_user(user)
        cache_service.put_user(user_id, user_data_object)
    return UserDataObject.to_user(user_data_object)


def clear_all_user_cache():
    '''
    清楚所有用户缓存
    :return:
    '''
    user_list = User.query.all()
    for user in user_list:
        cache_service.delete_user(user.id)


def clear_all_user_settings_cache():
    '''
    清楚所有用户设置缓存
    :return:
    '''
    user_list = User.query.all()
    for user in user_list:
        cache_service.delete_user_setting(user.id)


def replenish_promotion_qrcode_url():
    '''
    补充推广二维码
    :return:
    '''
    user_list = User.query.all()
    for user in user_list:
        if user.promotion_qrcode_url is None or user.promotion_qrcode_url == '':
            p_code_qrcode_url = get_promotion_qrcode_url(user.promotion_code, config.ENV)
            db.session.execute(update(User).where(User.id == user.id).values(promotion_qrcode_url=p_code_qrcode_url))
            print('user_id:' + str(user.id) + ' promotion_qrcode_url:' + p_code_qrcode_url)
    db.session.commit()


def get_user_english_level_chinese_abbreviation_and_pcode(user_id):
    '''
    根据用户ID获取英文水平中文简称，如：初级、推广码、会员等级
    :param user_id:
    :return:
    '''
    user = get_user(user_id)
    # 可用分钟数，转换为int
    available_minutes = int(user.available_minutes)
    if user.member_type == 0:
        membership_level = '普通用户'
        if available_minutes <=0:
            available_minutes = 0
    else:
        membership_level = 'VIP'
        # member_expiration_reminder_days = system_service.get_system_config_value(SystemConfigEnum.MemberExpirationRemindeDdays.value[0])
        # days = 7 if member_expiration_reminder_days is None  else member_expiration_reminder_days
        # available_days = (user.expiration_time - datetime.now()).days
        # if available_days <= days:
        #     membership_level = '即将到期'
    # if user.membership_level == 1:
    #     membership_level = '银牌会员'
    # elif user.membership_level == 2:
    #     membership_level = '金牌会员'
    # elif user.membership_level == 3:
    #     membership_level = '钻石会员'
    # if user.membership_level != 0 and user.expiration_time < datetime.now():
    #     available_minutes = 0
    # else:
    #     available_minutes = user.available_minutes
    # 获取 expiration_time 字段的日期部分，并将其转换为字符串
    expiration_date_str = None
    if user.expiration_time is not None:
        expiration_date_str = user.expiration_time.strftime('%Y-%m-%d')
    if user is not None and user.english_level is not None and EnglishLevels.get_enum_by_value(user.english_level) is not None and user.english_level != '':
        return EnglishLevels.get_enum_by_value(user.english_level).chinese_abbreviation, user.promotion_code, \
               membership_level, user.member_type, expiration_date_str, available_minutes, user

    return None, user.promotion_code, membership_level, user.member_type, expiration_date_str, available_minutes, user

def get_user_english_level(user_id):
    '''
    根据用户ID获取英文水平中文简称，如：初级
    :param user_id:
    :return:
    '''
    user = get_user(user_id)
    if user is not None and user.english_level is not None and user.english_level != '':
        return EnglishLevels.get_enum_by_value(user.english_level).level_en
    return None

def increase_user_validity_period(user_id, business_type):
    '''
    增加用户的有效期
    :param user_id: 用户ID
    :param business_type: 业务类型，1：银牌会员，2：金牌会员，3：钻石会员
    :return:
    '''
    # 月卡添加30天，季卡添加90天，年卡添加365天
    if business_type == 0:
        days = 3
    elif business_type == 1:
        days = 30
    elif business_type == 2:
        days = 90
    # 618 活动 赠送180天
    elif business_type == 3:
        days = 545
    elif business_type == 4:
        days = 730
    # 618 活动 赠送1年
    elif business_type == 5:
        days = 1460
    else:
        raise BizException(ErrorCode.PARAM_IS_VALID)
    user = get_user(user_id)
    if user is None:
        raise BizException(ErrorCode.USER_IS_NOT_EXIST)
    expiration_time = user.expiration_time
    if expiration_time is None:
        expiration_time = datetime.now()
    # 如果用户本身是会员并且expiration_time不超过当前时间增加有效期，重新计算expiration_time=expiration_time+days
    if user.member_type == 1 and expiration_time > datetime.now():
        expiration_time = expiration_time + timedelta(days=days)
    elif user.member_type == 0:
        expiration_time = datetime.now() + timedelta(days=days)
    # member_type = 1 为付费VIP会员
    db.session.execute(update(User).where(User.id == user.id).values(expiration_time=expiration_time, member_type=1, update_time=time.time()))
    # 更新用户数据后清除用户缓存
    cache_service.delete_user(user_id)


def increase_user_available_minutes(user_id, business_type):
    incremental = 0
    if business_type == 1:
        incremental = 1800
    elif business_type == 2:
        incremental = 7200
    elif business_type == 3:
        incremental == 21600
    else:
        raise BizException(ErrorCode.PARAM_IS_VALID)
    user = get_user(user_id)
    db.session.execute(update(User).where(User.id == user.id).values(
        available_minutes=(user.available_minutes + incremental), update_time=time.time())
        )


def increase_user_given_available_minutes(user_id, increment):
    user = get_user(user_id)
    db.session.execute(update(User).where(User.id == user.id).values(
        available_minutes=(user.available_minutes + increment),
        complimentary_minutes=(user.complimentary_minutes + increment),
        update_time=time.time())
    )
    db.session.commit()


def set_the_member_as_a_normal_user(user_id):
    '''
    将会员设置为普通用户
    :param user_id:
    :return:
    '''
    user = get_user(user_id)
    db.session.execute(update(User).where(User.id == user.id).values(member_type=0, update_time=time.time())
    )
    # 删除用户缓存
    cache_service.delete_user(user_id)


def get_invitation_detail(user_id):
    '''
    获取邀请详情
    :param user_id:
    :return:
    '''
    user = get_user(user_id)
    if user is None:
        raise BizException(ErrorCode.USER_IS_NOT_EXIST)
    # 获取邀请码
    promotion_code = user.promotion_code
    # 获取邀请人数
    num_of_invitations = User.query.filter_by(referral_code=promotion_code).count()
    # 获取已学习的分钟数
    num_of_used_minutes = user.used_minutes
    # 获取已赠送的分钟数
    complimentary_minutes = user.complimentary_minutes
    return promotion_code, num_of_invitations, num_of_used_minutes, complimentary_minutes


def daily_sign_in(user_id):
    '''
    用户每日签到
    :param user_id:
    :return:
    '''
    user = get_user(user_id)
    if user is None:
        raise BizException(ErrorCode.USER_IS_NOT_EXIST)
    # 获取当前日期
    current_date = datetime.now().date()
    # 将日期转换为 YYMMDD 格式的整数，例如：20240224
    formatted_date = int(current_date.strftime('%Y%m%d'))

    # 获取当前日期用户user_chat_log表中user_message不为空并且不为空字符串的create_time升序排列的第一条记录
    first_user_chat_log = UserChatLog.query.filter_by(user_id=user_id).filter(UserChatLog.user_message != None,
                                                                              UserChatLog.date == formatted_date,
                                                                              UserChatLog.user_message != '').order_by(asc(UserChatLog.create_time)).first()
    if first_user_chat_log is None:
        raise BizException(ErrorCode.DAILY_LEARNING_TIME_IS_LESS_THAN_2_MINUTES_CANNOT_SIGN_IN)
    # 获取当前日期用户user_chat_log表中user_message不为空的create_time降序排列的第一条记录
    last_user_chat_log = UserChatLog.query.filter_by(user_id=user_id).filter(UserChatLog.user_message != None,
                                                                             UserChatLog.date == formatted_date,
                                                                             UserChatLog.user_message != '').order_by(desc(UserChatLog.create_time)).first()
    if first_user_chat_log.create_time == last_user_chat_log.create_time:
        raise BizException(ErrorCode.DAILY_LEARNING_TIME_IS_LESS_THAN_2_MINUTES_CANNOT_SIGN_IN)
    # 获取用户当天的学习时长
    learning_time = (last_user_chat_log.create_time - first_user_chat_log.create_time)
    if learning_time < 120:
        raise BizException(ErrorCode.DAILY_LEARNING_TIME_IS_LESS_THAN_2_MINUTES_CANNOT_SIGN_IN)
    # 获取签到记录
    user_sign_in = UserSignIn.query.filter_by(user_id=user_id, date=formatted_date).first()
    # 打印签到记录
    print(user_sign_in)
    if user_sign_in is not None:
        raise BizException(ErrorCode.USER_HAS_SIGN_IN)
    # 获取昨天的日期
    yesterday_date = current_date - timedelta(days=1)
    # 将日期转换为 YYMMDD 格式的整数，例如：20240223
    formatted_yesterday_date = int(yesterday_date.strftime('%Y%m%d'))
    # 获取连续签到天数
    continuous_sign_in_days = get_continuous_sign_in_days(user_id, formatted_yesterday_date)
    sign_in_given_minutes = system_service.get_system_config_value('sign_in_given_minutes')
    # 插入签到记录
    data = UserSignIn(user_id, formatted_date, continuous_sign_in_days, sign_in_given_minutes)
    db.session.add(data)
    db.session.commit()
    # 获取签到奖励，给用户增加5分钟的使用时长
    increase_user_given_available_minutes(user_id, sign_in_given_minutes)
    # 删除用户缓存
    cache_service.delete_user(user_id)
    return sign_in_given_minutes, continuous_sign_in_days


def get_continuous_sign_in_days(user_id, yesterday_date):
    '''
    获取连续签到天数,
    :param user_id:
    :param formatted_date: 格式化后的日期，例如：20240221
    :return:
    '''
    # 查看yesterday_date是否签到，如果签到则连续签到天数加1，否则连续签到天数清零
    user_sign_in = UserSignIn.query.filter_by(user_id=user_id, date=yesterday_date).first()
    if user_sign_in is not None:
        # 获取连续签到天数
        return (user_sign_in.continuous_sign_in_times + 1)

    else:
        return 1


def get_user_has_signed_in(user_id):
    '''
    获取用户是否已经签到
    :param user_id:
    :return:
    '''
    # 获取当前日期
    current_date = datetime.now().date()
    # 将日期转换为 YYMMDD 格式的整数
    formatted_date = int(current_date.strftime('%Y%m%d'))
    user_sign_in = UserSignIn.query.filter_by(user_id=user_id, date=formatted_date).first()
    if user_sign_in is not None:
        return True
    return False


def get_gift_duration_for_recommender(recommender_id):
    '''
    获取用户通过推荐获得的赠送时长
    :param recommender_id:
    :return:
    '''
    # select sum(gift_duration) from user_referrer where referrer_id = #recommender_id#
    result = db.session.query(func.sum(UserReferrer.referrer_gift_duration)).filter_by(referrer_id=recommender_id).first()
    # 如果没有推荐人，则返回0
    if result[0] is None:
        return 0
    return result[0]


def update_has_finish_test(user_id, status):
    '''
    更新用户已经完成测试
    :param user_id:
    :param status: 0 未完成，1 完成，2 暂缓
    :return:
    '''
    db.session.execute(update(User).where(User.id == user_id).values(has_finish_test=status, update_time=time.time())
    )
    db.session.commit()
    # 删除用户缓存
    cache_service.delete_user(user_id)


def get_last_has_report_user_chat(user_id, topic_code, mode):
    '''
    获取用户最后一次聊天conversation_id order by UserChatLog.create_time desc limit 1
    :param user_id:
    :param topic_code:
    :param mode: 0 实战，1 外教
    :return:
    '''
    # 获取用户最后一次有报告的聊天
    # where report is not null and report != ''
    last_user_chat = UserChat.query.filter_by(user_id=user_id, topic_code=topic_code, mode=mode)\
        .filter(UserChat.report != None, UserChat.report != '').order_by(desc(UserChat.create_time)).limit(1).first()
    if last_user_chat is None:
        return None
    return last_user_chat



def get_last_user_chat_conversation_id(user_id, topic_code, mode):
    '''
    获取用户最后一次聊天conversation_id order by UserChatLog.create_time desc limit 1
    :param user_id:
    :param topic_code:
    :param mode: 0 实战，1 外教
    :return:
    '''
    # 获取用户最后一次聊天
    last_user_chat = UserChat.query.filter_by(user_id=user_id, topic_code=topic_code, mode=mode).order_by(desc(UserChat.create_time)).limit(1).first()
    if last_user_chat is None:
        return 0
    return last_user_chat.conversation_id


def get_topic_goal(user_id, conversation_id, topic_code):
    '''
    获取话题目标
    :param user_id:
    :param conversation_id:
    :param topic_code:
    :return:
    '''
    # UserChat和SystemSceneTopics通过topic_code关联，user_id和conversation_id作为UserChat的查询条件
    # Get the SQLAlchemy session
    session = db.session

    # 获取topic_code判断是否是社区主题还是系统默认
    if not topic_util.is_community_topic(topic_code):
        # Perform the join
        query = session.query(UserChat, SystemSceneTopics).filter(UserChat.topic_code == SystemSceneTopics.topic_code)\
            .filter(UserChat.user_id == user_id, UserChat.conversation_id == conversation_id)
        # Execute the query
        result = query.first()
        # 通过topic_code 进行user_chat关联system_scene_topic查询goal

        if result is None:
            # 抛异常
            raise BizException(ErrorCode.DATA_IS_EMPTY)
        
        return result.SystemSceneTopics.target_english
    else:
        # 获取community_scene_topics表中的target_english
        community_scene_topics = CommunitySceneTopics.query.filter_by(topic_code=topic_code).first()
        return community_scene_topics.target_english


def plus_user_ielts_tried_times(user_id, part1_topic_id, part2_3_topic_id, practice_type):
    '''
    更新用户试用一次雅思测试
    :param user_id:
    :param practice_type
    :return:
    '''
    # 先检查UserIeltsInfo记录是否存在
    user_ielts_info = UserIeltsInfo.query.filter_by(user_id=user_id).first()
    if user_ielts_info is None:
        user_ielts_info = UserIeltsInfo(user_id, None,None)
        db.session.add(user_ielts_info)
        db.session.flush()
        if practice_type == 1:
            if part1_topic_id is not None:
                db.session.execute(update(UserIeltsInfo).where(UserIeltsInfo.id == user_ielts_info.id).values(part1_exercise_tried_times=1, update_time=time.time())
                                   )
            else:
                db.session.execute(update(UserIeltsInfo).where(UserIeltsInfo.id == user_ielts_info.id).values(part2_3_exercise_tried_times=1, update_time=time.time())
                                   )
        else:
            if part2_3_topic_id is not None:
                db.session.execute(update(UserIeltsInfo).where(UserIeltsInfo.id == user_ielts_info.id).values(part2_3_exercise_tried_times=1, update_time=time.time())
                                   )
            else:
                db.session.execute(update(UserIeltsInfo).where(UserIeltsInfo.id == user_ielts_info.id).values(part2_3_mock_tried_times=1, update_time=time.time())
                                   )
    else:
        if practice_type == 1:
            if part1_topic_id is not None:
                db.session.execute(update(UserIeltsInfo).where(UserIeltsInfo.id == user_ielts_info.id).values(part1_exercise_tried_times= user_ielts_info.part1_exercise_tried_times + 1, update_time=time.time())
                                   )
            else:
                db.session.execute(update(UserIeltsInfo).where(UserIeltsInfo.id == user_ielts_info.id).values(part2_3_exercise_tried_times=user_ielts_info.part2_3_exercise_tried_times + 1, update_time=time.time())
                                   )
        else:
            if part2_3_topic_id is not None:
                db.session.execute(update(UserIeltsInfo).where(UserIeltsInfo.id == user_ielts_info.id).values(part2_3_exercise_tried_times=user_ielts_info.part2_3_exercise_tried_times + 1, update_time=time.time())
                                   )
            else:
                db.session.execute(update(UserIeltsInfo).where(UserIeltsInfo.id == user_ielts_info.id).values(part2_3_mock_tried_times=user_ielts_info.part2_3_mock_tried_times + 1, update_time=time.time())
                                   )
    db.session.commit()


def set_learning_phase(user_id, learning_phase):
    '''
    设置学习阶段
    :param user_id:
    :param learning_phase:
    :return:
    '''
    user_settings = UserSettings.query.filter_by(user_id=user_id).first()

    # 如果不存在则插入
    if user_settings is None:
        user_settings = UserSettings(user_id, 1, 1, learning_phase, 1, None)
        db.session.add(user_settings)
    else:
        db.session.execute(update(UserSettings).where(UserSettings.id == user_settings.id).values(learning_phase=learning_phase,
                                                                                              update_time=time.time()))
        cache_service.delete_user_setting(user_id)
    db.session.commit()
    return learning_phase

def set_is_mixed_teaching(user_id, is_mixed_teaching):
    '''
    设置是否中英混合教学
    :param user_id:
    :param is_mixed_teaching:
    :return:
    ''' 
    user_settings = UserSettings.query.filter_by(user_id=user_id).first()

    # 如果不存在则插入
    if user_settings is None:
        user_settings = UserSettings(user_id, 1, 1, 1, is_mixed_teaching, None)
        db.session.add(user_settings)
    else:
        db.session.execute(update(UserSettings).where(UserSettings.id == user_settings.id).values(is_mixed_teaching=is_mixed_teaching,                                                                                             update_time=time.time()))
        cache_service.delete_user_setting(user_id)
    db.session.commit()
    return is_mixed_teaching


def get_user_used_minutes(user_id):
    '''
    获取用户已使用时长
    :param user_id:
    :return:
    '''
    user = User.query.filter_by(id=user_id).first()
    if user is None:
        return 0
    return user.used_minutes

