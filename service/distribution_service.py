from sqlalchemy.orm import aliased

from flask_sqlalchemy_ext.flask_sqlalchemy_txns import transactional
from sqlalchemy import or_, and_, func, update
from models.distributor_commission_records import DistributorCommissionRecords
from models.distributor_order import DistributorOrder
from models.distributor import Distributor
from models.user_referrer import UserReferrer
from models.sms_verification import SmsVerification
from constans.distribution_rate import DISTRIBUTION_RATE, MANAGEMENT_FEE_RATE
from db import db
from exception.biz_exception import BizException
from error_code import ErrorCode
import time
from sso import authentication

from models.distributor import Distributor
from models.distributor_order import DistributorOrder
from models.payment_flow import PaymentFlow
from models.distributor_settlement_statement import DistributorSettlementStatement
from models.user import User
from service import user_service

import threading
from utils import feishu_webhook_sender
import config
from utils import string_utils
from utils.pagination_util import format_pagination_data
from datetime import datetime
from utils import date_utils
from utils import code_gen_util
from app_init import app


env = config.ENV

## 分销员申请通知机器人
distribution_notify_webhook = 'https://open.feishu.cn/open-apis/bot/v2/hook/06b8df2d-5329-4a04-bc5c-d7af4322e1f5'


'''
用户申请分销员
'''
@transactional
def apply_distributor(name, phone, verification_code, province_code, city_code, referral_code, organization_code, sessionId, appKey):
    # 根据phone查询SmsVerification中是否存在记录，并且验证码是否正确和是否过期（5分钟内有效）
    sms_verification = SmsVerification.query.filter_by(phone=phone).first()
    if sms_verification is None:
        raise BizException(ErrorCode.VERIFICATION_CODE_IS_VALID)
    # 判断验证码是否正确
    if sms_verification.verification_code != verification_code:
        raise BizException(ErrorCode.VERIFICATION_CODE_IS_VALID)
    # 判断验证码是否过期
    if time.time() - sms_verification.code_generate_time > 300:
        raise BizException(ErrorCode.VERIFICATION_CODE_IS_VALID)

    #如果referral_code不为空，则通过referral_code去匹配user表中的promotion_code查询出用户id
    #保持当前申请者的organization_code跟推荐人一样，属于统一组织。
    parent_user_id = 0
    if string_utils.is_not_blank(referral_code):
        user = user_service.get_user_info_by_pcode_or_phone(referral_code)
        if user is not None:
            parent_user_id = user.id
            # 通过user.id查询Distributor中的organization_code
            if string_utils.is_blank(organization_code):
                distributor = Distributor.query.filter_by(user_id=parent_user_id).first()
                if distributor is not None:
                    organization_code = distributor.organization_code

    # 包含session信息则为用户登录后的入口进来
    if string_utils.is_not_blank(sessionId) and string_utils.is_not_blank(appKey):
        #根据sessionId和appKey查询是否存在用户
        session_user = authentication.get_session_user(sessionId, appKey)
        #如果存在用户则不需要创建User,只需要创建Distributor
        if session_user is not None:
            user_id = session_user.user_id
            # 创建分销员
            apply_save_distributor(name, phone, user_id, province_code, city_code, parent_user_id, organization_code)
        else:
            # 如果不存在用户则创建用户和分销商
            user = user_service.create_user(phone, referral_code)
            # 创建分销员
            apply_save_distributor(name, phone, user.id, province_code, city_code, parent_user_id, organization_code)
    # 不是用户登录后的入口进来时
    else:
        # 如果不存在用户则创建用户和分销商
        user = user_service.create_user(phone, referral_code)
        # 创建分销员
        apply_save_distributor(name, phone, user.id, province_code, city_code, parent_user_id, organization_code)

    # 发送分销员申请飞书通知
    send_message_thread(name, phone, province_code, city_code)
    return True

def send_message_thread(name, phone, province_code, city_code):

    message = "线上环境分销员申请通知,姓名："+name+",手机号："+phone+",省份："+province_code+",城市："+city_code

    if env == 'develop':
        message = "测试环境分销员申请通知,姓名："+name+",手机号："+phone+",省份："+province_code+",城市："+city_code

    thread = threading.Thread(target=send_message, args=(message, distribution_notify_webhook))
    thread.start()

def send_message(message, webhook):

    feishu_webhook_sender.send_message(message, webhook)


def create_distributor(name, phone , user_id, province_code, city_code, parent_user_id, organization_code):
    """
    创建分销商
    :param name:
    :param phone:
    :param user_id:
    :param province_code:
    :param city_code:
    :param parent_user_id:
    :param organization_code:
    :return:
    """
    # 判断手机号是否已经申请过
    distributor = Distributor.query.filter_by(phone=phone).first()
    if distributor is not None:
        raise BizException(ErrorCode.PHONE_ALREADY_EXIST)
    # 判断用户是否已经申请过
    distributor = Distributor.query.filter_by(user_id=user_id).first()
    if distributor is not None:
        raise BizException(ErrorCode.DISTRIBUTOR_ALREADY_EXIST)
    distributor = Distributor(name, phone, user_id, province_code, city_code, parent_user_id, organization_code,1)
    db.session.add(distributor)
    db.session.flush()
    return distributor

def apply_save_distributor(nama, phone, user_id, province_code, city_code, parent_user_id, organization_code):
    """
    申请的时候保存分销商信息,如果根据user_id查询不存在则创建，否则更新
    :param nama:
    :param phone:
    :param user_id:
    :param province_code:
    :param city_code:
    :param parent_user_id:
    :param organization_code:
    :return:
    """

    # 判断手机号是否已经申请过
    distributor = Distributor.query.filter_by(phone=phone).first()
    if distributor is not None:
        raise BizException(ErrorCode.PHONE_ALREADY_EXIST)


    # 默认是一级分销商抽佣比例
    commission_rate = DISTRIBUTION_RATE
    management_fee_rate = MANAGEMENT_FEE_RATE
    # 二级分销商抽佣比例
    if parent_user_id is not None and parent_user_id != 0:
        # 更新推荐人推荐人数
        commission_rate = SECOND_DISTRIBUTION_RATE
        management_fee_rate = 0

    distributor = Distributor.query.filter_by(user_id=user_id).first()
    if distributor is None:
        distributor = Distributor(nama, phone, user_id, province_code, city_code, parent_user_id, organization_code,1)
        db.session.add(distributor)
        db.session.flush()

        # update
        db.session.execute(update(Distributor).where(Distributor.id == distributor.id).values(
            name=nama, phone=phone, province_code=province_code, city_code=city_code, parent_user_id=parent_user_id, organization_code=organization_code, status=1,
            commission_rate=commission_rate, management_fee_rate=management_fee_rate,
            update_time=time.time(), apply_time=time.time()
        ))
    else:
        distributor.name = nama
        distributor.phone = phone
        distributor.province_code = province_code
        distributor.city_code = city_code
        distributor.parent_user_id = parent_user_id
        distributor.organization_code = organization_code
        db.session.execute(update(Distributor).where(Distributor.id == distributor.id).values(
            name=nama, phone=phone, province_code=province_code, city_code=city_code, parent_user_id=parent_user_id, organization_code=organization_code, status=1,
            commission_rate=commission_rate, management_fee_rate=management_fee_rate,
            update_time=time.time(), apply_time=time.time()
        ))

def get_commission_info(user_id):
    """
    获取佣金信息：累计佣金、已结算金额、待结算金额
    :param user_id:
    :return:
    """
    # 获取分销商信息
    distributor = Distributor.query.filter_by(user_id=user_id).first()
    if distributor is None:
        raise BizException(ErrorCode.DISTRIBUTOR_IS_NOT_EXIST)
    # 获取累计佣金、已结算金额、待结算金额
    commission_info = {
        "total_commission": round((distributor.settled_amount + distributor.unsettled_amount) / 100, 2),
        "settled_amount": round(distributor.settled_amount / 100, 2),
        "unsettled_amount": round(distributor.unsettled_amount / 100, 2)
    }

    return commission_info

def get_my_distributor_base_info(user_id):
    """
    获取我的分销商基础信息（parent_user_id = user_id），包括：推广员总数、本月新增、活跃推广员（一周内推广用户大于0的）总数
    :param user_id:
    :return:
    """
    # 获取推广员总数
    total_count = Distributor.query.filter_by(parent_user_id=user_id).count()
    # 获取本月新增
    current_month = datetime.now().strftime('%Y%m')
    new_count = Distributor.query.filter_by(parent_user_id=user_id).filter(Distributor.create_time.like(current_month + '%')).count()
    # 获取活跃推广员总数
    active_count = get_active_distributors_count(user_id)
    # 获取我的分销商基础信息
    my_distributor_base_info = {
        "total_count": total_count,
        "new_count": new_count,
        "active_count": active_count
    }
    return my_distributor_base_info


def get_active_distributors_count(user_id):
    """
    获取活跃推广员总数
    活跃的定义：该推广员有推广的用户，且这些用户在过去7天内注册
    :param user_id: 当前用户ID
    :return: 活跃推广员数量
    """
    # 获取7天前的时间戳
    seven_days_ago = time.time() - (7 * 24 * 60 * 60)

    # 创建别名以区分不同的User表实例
    DistributorUser = aliased(User, name='distributor_user')  # 推广员对应的用户
    CustomerUser = aliased(User, name='customer_user')  # 被推广的客户

    # 使用关联查询获取活跃推广员数量
    # 1. 找到parent_user_id为user_id的所有推广员
    # 2. 关联这些推广员对应的用户表获取promotion_code
    # 3. 关联用户表找到referral_code匹配promotion_code且在7天内注册的用户
    # 4. 对推广员ID进行distinct计数

    active_distributors_count = db.session.query(Distributor.id).distinct().join(
        DistributorUser, DistributorUser.id == Distributor.user_id  # 关联推广员对应的用户
    ).join(
        CustomerUser, CustomerUser.referral_code == DistributorUser.promotion_code  # 关联被推广的用户
    ).filter(
        Distributor.parent_user_id == user_id,  # 我的推广员
        CustomerUser.create_time >= seven_days_ago  # 7天内注册的用户
    ).count()

    return active_distributors_count



def get_my_distributor_list(user_id, keyword, page_index, page_size):
    """
    获取我的分销商列表
    :param user_id: 我的用户ID
    :param keyword: 关键字
    :param page_index: 当前页码
    :param page_size: 每页数量
    :param page_size:
    :return:
    """
    # 计算偏移量
    offset = (page_index - 1) * page_size
    
    # 构建基础查询条件
    base_condition = (Distributor.parent_user_id == user_id)
    if keyword is not None and keyword != '':
        base_condition = and_(base_condition, 
                             or_(Distributor.name.like('%' + keyword + '%'), 
                                Distributor.phone.like('%' + keyword + '%')))
    
    # 获取总记录数
    total_count = db.session.query(Distributor).filter(base_condition).count()
    
    # 如果keyword不为空，则根据关键字keyword去跟name和phone进行模糊匹配
    if keyword is not None and keyword != '':
        distributors = db.session.query(Distributor, User).join(
            User, Distributor.user_id == User.id
        ).filter(
            (Distributor.parent_user_id == user_id) &
            ((Distributor.name.like('%' + keyword + '%')) | (Distributor.phone.like('%' + keyword + '%')))
        ).order_by(Distributor.create_time.desc()).offset(offset).limit(page_size).all()
    else:
        distributors = db.session.query(Distributor, User).join(
            User, Distributor.user_id == User.id
        ).filter(
            Distributor.parent_user_id == user_id
        ).order_by(Distributor.create_time.desc()).offset(offset).limit(page_size).all()
    # 将查询结果转换为字典列表
    distributors_list = []
    for distributor_tuple in distributors:
        distributor = distributor_tuple[0]  # 获取元组中的Distributor对象
        user = distributor_tuple[1] # 获取元组中的User对象
        phone = desensitize_phone_number(distributor.phone)
        distributors_list.append({
            "account_no": user.account_no,
            "avatar_url": user.avatar_url,
            "name": desensitize_name(distributor.name),
            "phone": phone,
            "contribution_management_fee": round(distributor.contribution_management_fee / 100, 2),
            "invited_count": distributor.invited_count,
            "distributor_order_count": distributor.distributor_order_count
        })
    return format_pagination_data(distributors_list, total_count, page_index, page_size)



def get_distributor_info(user_id):
    """
    获取分销商信息
    :param user_id:
    :return:
    """
    # 使用关联查询获取分销商和用户信息
    result = db.session.query(Distributor, User).join(
        User, Distributor.user_id == User.id
    ).filter(
        Distributor.user_id == user_id
    ).first()

    if result is None:
        raise BizException(ErrorCode.DISTRIBUTOR_IS_NOT_EXIST)

    distributor, user = result

    distributor_info = {
        # 姓名
        "name": distributor.name,
        # 用户头像
        "avatar_url": user.avatar_url,
        # 已邀请人数
        "invited_count": distributor.invited_count,
        #累计佣金
        "total_commission": round((distributor.settled_amount + distributor.unsettled_amount) / 100, 2),
        # 已结算金额
        "settled_amount": round(distributor.settled_amount / 100, 2),
        # 待结算金额
        "unsettled_amount": round(distributor.unsettled_amount / 100, 2),
        # 是否已认证
        "is_authenticated": distributor.is_real_name_authentication,
        # 上级用户ID
        "parent_user_id": distributor.parent_user_id,
        # 状态
        "status": distributor.status
    }
    return distributor_info



def desensitize_name(name):
    """
    将名字中间的字隐藏
    :param name:
    :return:
    """
    if name is None:
        return ''
    if len(name) == 2:
        return name[0] + '*'
    elif len(name) > 2:
        return name[0] + '*' * (len(name) - 2) + name[-1]
    else:
        return name

def desensitize_phone_number(phone_number):
    """
    将手机号中间4位隐藏
    :param phone_number:
    :return:
    """
    return phone_number[:3] + '****' + phone_number[-4:]


def get_my_customer_info(user_id):
    """
    获取我的客户信息，包括：我的客户总数、本月新增数
    :param user_id:
    :return:
    """
    user = user_service.get_user(user_id)
    # 我的客户总数，通过User表的referral_code等于user_id对应的User.promotion_code来查询
    total_count = User.query.filter_by(referral_code=user.promotion_code).count()
    # 本月新增数：查出本月最小的unixtime，然后User.register_time > 本月最小的unixtime
    # 获取当前月份的第一天的 Unix 时间戳
    now = datetime.now()
    first_day_of_month = datetime(now.year, now.month, 1)
    first_day_timestamp = time.mktime(first_day_of_month.timetuple())

    # 查询本月新增的用户数量
    new_users_count = User.query.filter(
        User.referral_code == user.promotion_code,
        User.register_time >= first_day_timestamp
    ).count()
    # 获取我的客户信息
    my_customer_info = {
        "total_count": total_count,
        "new_count": new_users_count
    }
    return my_customer_info


def get_my_customer_list(user_id, keyword, page_index, page_size):
    """
    获取我的客户列表
    :param user_id:
    :param keyword:
    :param page_index:
    :param page_size:
    :return:
    """
    user = user_service.get_user(user_id)
    # 计算偏移量
    offset = (page_index - 1) * page_size
    
    # 构建基础查询 - 关联PaymentFlow表获取支付信息
    base_query = db.session.query(
        User,
        func.count(PaymentFlow.id).label('payment_count'),
        func.sum(PaymentFlow.actual_fee).label('total_actual_fee')
    ).outerjoin(
        PaymentFlow, 
        and_(User.id == PaymentFlow.user_id, PaymentFlow.pay_status == 1)
    ).filter(
        User.referral_code == user.promotion_code
    ).group_by(User.id)
    
    # 如果keyword不为空，则根据关键字keyword去跟name和phone进行模糊匹配
    if keyword is not None and keyword != '':
        base_query = base_query.filter(
            or_(User.nickname.like(f'%{keyword}%'), User.phone.like(f'%{keyword}%'))
        )
    
    # 获取总记录数
    # 如果keyword不为空，则根据关键字keyword去跟name和phone进行模糊匹配
    if keyword is not None and keyword != '':
        total_count = base_query.count()
    else:
        total_count = db.session.query(User).filter(
            User.referral_code == user.promotion_code
        ).count()
    
    # 执行查询并添加排序、分页
    query_results = base_query.order_by(User.register_time.desc()).offset(offset).limit(page_size).all()
    
    # 将查询结果转换为字典列表
    users_list = []
    for result in query_results:
        user = result[0]
        payment_count = result[1] or 0
        total_actual_fee = result[2] or 0
        
        users_list.append({
            "avatar_url": user.avatar_url,
            "nickname": user.nickname,
            "account_no":user.account_no,
            "register_time": user.register_time,
            "payment_count": payment_count,
            "total_actual_fee": round(total_actual_fee / 100, 2)  # 转换为元并四舍五入到2位小数
        })
    
    return format_pagination_data(users_list, total_count, page_index, page_size)


"""
我和我下级的分销订单列表（根据关键字分页查询）
"""
def get_my_distributor_order_list(user_id, keyword, page_index, page_size, is_settle=None, is_refunded=None):
    """
    获取我的分销订单列表，关联逻辑：
    1、自己的直接分销订单：查询DistributorOrder.distributor_user_id = user_id
    2、自己下级的分销订单：查询Distributor.parent_user_id = user_id，然后DistributorOrder.distributor_user_id = Distributor.user_id
    :param user_id: 用户ID
    :param keyword: 搜索关键字
    :param page_index: 页码
    :param page_size: 每页数量
    :param is_settle: 是否已结算
    :param is_refunded: 是否已退款
    :return: 分销订单列表，字段包括：分销订单号(DistributorOrder.order_no)、用户的account_no(payment_flow对用的user_id的User.account_no)、用户昵称(User.nickname)、
                                支付方式(payment_flow.pay_client_type)、支付时间(payment_flow.pay_time)、结算时间(DistributorOrder.settle_time)、
                                会员类型(payment_flow.business_type)、支付金额(payment_flow.actual_fee)、
                                分销员用户ID(DistributorOrder中的distributor_user_id对应的User.account_no)、
                                分销员用户昵称(DistributorOrder中的distributor_user_id对应的User.nickname)、
                                佣金金额(DistributorCommissionRecords.commission_type=1的DistributorCommissionRecords.commission_amount)、
                                管理费金额（DistributorCommissionRecords.commission_type=2的DistributorCommissionRecords.commission_amount)、
                                是否已退款（payment_flow.pay_status = 11）
    """
    # 计算偏移量
    offset = (page_index - 1) * page_size
    
    # 创建别名以区分不同表的User实例
    CustomerUser = aliased(User, name='customer_user')  # 客户用户
    DistributorUser = aliased(User, name='distributor_user')  # 分销员用户
    PaymentFlowAlias = aliased(PaymentFlow, name='payment_flow')  # 支付流水
    DistributorAlias = aliased(Distributor, name='distributor')  # 分销商
    
    # 创建子查询获取佣金金额(commission_type=1)
    commission_subquery = db.session.query(
        DistributorCommissionRecords.distributor_order_id,
        func.sum(DistributorCommissionRecords.commission_amount).label('commission_amount')
    ).filter(
        DistributorCommissionRecords.commission_type == 1
    ).group_by(DistributorCommissionRecords.distributor_order_id).subquery()
    
    # 创建子查询获取管理费金额(commission_type=2)
    management_fee_subquery = db.session.query(
        DistributorCommissionRecords.distributor_order_id,
        func.sum(DistributorCommissionRecords.commission_amount).label('management_fee_amount')
    ).filter(
        DistributorCommissionRecords.commission_type == 2
    ).group_by(DistributorCommissionRecords.distributor_order_id).subquery()
    
    # 构建基础查询
    base_query = db.session.query(
        DistributorOrder,
        CustomerUser.account_no.label('customer_account_no'),
        CustomerUser.nickname.label('customer_nickname'),
        CustomerUser.phone.label('customer_phone'),
        DistributorUser.account_no.label('distributor_account_no'),
        commission_subquery.c.commission_amount,
        management_fee_subquery.c.management_fee_amount,
        PaymentFlowAlias.pay_status.label('payment_status'),
        PaymentFlowAlias.pay_client_type.label('pay_client_type'),
        PaymentFlowAlias.pay_time.label('pay_time'),
        PaymentFlowAlias.business_type.label('business_type'),
        DistributorAlias.name.label('distributor_name')
    ).join(
        DistributorUser, DistributorOrder.distributor_user_id == DistributorUser.id
    ).join(
        DistributorAlias, DistributorOrder.distributor_user_id == DistributorAlias.user_id
    ).outerjoin(
        PaymentFlowAlias, DistributorOrder.payment_flow_id == PaymentFlowAlias.id
    ).outerjoin(
        CustomerUser, PaymentFlowAlias.user_id == CustomerUser.id
    ).outerjoin(
        commission_subquery, DistributorOrder.id == commission_subquery.c.distributor_order_id
    ).outerjoin(
        management_fee_subquery, DistributorOrder.id == management_fee_subquery.c.distributor_order_id
    )
    
    # 添加过滤条件：自己的订单或下级的订单
    my_distributors_subquery = db.session.query(Distributor.user_id).filter(
        Distributor.parent_user_id == user_id
    ).subquery()
    
    base_query = base_query.filter(
        or_(
            DistributorOrder.distributor_user_id == user_id,
            DistributorOrder.distributor_user_id.in_(my_distributors_subquery)
        )
    )
    
    # 如果有关键字，添加搜索条件
    if keyword is not None and keyword != '':
        base_query = base_query.filter(
            or_(
                CustomerUser.account_no.like(f'%{keyword}%'),
                CustomerUser.nickname.like(f'%{keyword}%'),
                CustomerUser.phone.like(f'%{keyword}%'),
                DistributorOrder.order_no.like(f'%{keyword}%')
            )
        )
    
    # 如果指定了结算状态，添加过滤条件
    if is_settle is not None:
        base_query = base_query.filter(DistributorOrder.settle_status == is_settle)
    
    # 如果指定了退款状态，添加过滤条件
    if is_refunded is not None:
        if is_refunded:
             base_query = base_query.filter(PaymentFlowAlias.pay_status == 11)
        # else:
        #     base_query = base_query.filter(or_(PaymentFlowAlias.pay_status != 11, PaymentFlowAlias.pay_status == None))
    
    # 获取总记录数
    total_count = base_query.count()
    
    # 添加排序、分页
    query_results = base_query.order_by(
        DistributorOrder.create_time.desc()
    ).offset(offset).limit(page_size).all()
    
    # 构建结果列表
    distributor_orders_list = []
    for result in query_results:
        order = result[0]  # DistributorOrder对象
        
        # 判断是否已退款
        is_refunded_status = result.payment_status == 11 if result.payment_status is not None else False
        # 将类型为datetime 的字段result.pay_time转换为格式为 yyyy-MM-dd HH:mm:ss的字符串
        pay_time_string = None
        if result.pay_time is not None:
            pay_time_string = date_utils.date_to_string(result.pay_time)

        distributor_orders_list.append({
            "order_no": order.order_no,
            "customer_account_no": result.customer_account_no,
            "customer_nickname": result.customer_nickname,
            "pay_client_type": result.pay_client_type,
            "pay_time": pay_time_string,
            "settle_time": date_utils.seconds_to_time_string(order.settle_time),
            "business_type": result.business_type,
            "actual_fee": round(order.actual_fee / 100, 2) if order.actual_fee else 0,
            "distributor_account_no": result.distributor_account_no,
            "distributor_name": desensitize_name(result.distributor_name),
            "commission_amount": round(result.commission_amount / 100, 2) if result.commission_amount else 0,
            "management_fee_amount": round(result.management_fee_amount / 100, 2) if result.management_fee_amount else 0,
            "is_refunded": 1 if is_refunded_status else 0,
            "settle_status": order.settle_status
        })

    return format_pagination_data(distributor_orders_list, total_count, page_index, page_size)


## 我的结算记录（根据结算时间区间分页查询）
def get_my_settlement_record_list(user_id, start_date, end_date, page_index, page_size):
    """
    获取我的结算记录列表
    :param user_id:
    :param start_date: 格式为********
    :param end_date: 格式为********
    :param page_index:
    :param page_size:
    :return: 结算记录列表(字段来源于表DistributorSettlementStatement)，包含字段：结算单号、结算时间、结算金额、结算方式、结算状态
    """
    # 计算偏移量
    offset = (page_index - 1) * page_size
    # 构建基础查询
    base_query = db.session.query(
        DistributorSettlementStatement
    ).filter(
        DistributorSettlementStatement.distributor_user_id == user_id
    )

    # 如果有起止日期，添加过滤条件
    if start_date is not None and start_date != '' and end_date is not None and end_date != '':
        base_query = base_query.filter(
            DistributorSettlementStatement.settle_date >= start_date,
            DistributorSettlementStatement.settle_date <= end_date
        )

    # 获取总记录数
    total_count = base_query.count()

    # 添加排序、分页
    query_results = base_query.order_by(
        DistributorSettlementStatement.settle_time.desc()
    ).offset(offset).limit(page_size).all()

    # 构建结果列表
    settlement_records_list = []
    for result in query_results:
        settlement_records_list.append({
            "settle_no": result.settle_no,
            "settle_time": result.settle_time,
            "settle_amount": round(result.settle_amount / 100, 2) if result.settle_amount else 0,
            "settle_type": result.settle_type,
            "settle_status": result.settle_status
        })

    return format_pagination_data(settlement_records_list, total_count, page_index, page_size)


@transactional
def add_distributor_order_and_clearing(out_trade_no):
    """
    创建分销订单并实时清分
    :param out_trade_no:
    :return:
    """
    # 

    # 通过payment_flow_id查询payment_flow
    payment_flow = PaymentFlow.query.filter_by(out_trade_no=out_trade_no).first()
    if payment_flow is None:
        return
    # 生成分销单号
    order_no = code_gen_util.get_uuid(payment_flow.user_id)
    # 创建分销商订单
    distributor_order = DistributorOrder(payment_flow.user_id, payment_flow, order_no, payment_flow.total_fee, payment_flow.actual_fee, payment_flow.discount_amount,
                                         payment_flow.pay_time.timestamp(), payment_flow.pay_date)
    db.session.add(distributor_order)
    db.session.flush()

    # 查询UserReferrer
    user_referrer = UserReferrer.query.filter_by(user_id=payment_flow.user_id).first()

    # 如果user_referrer存在且user_referrer.referrer_id不等于0，则更新user_referrer的user_total_recharge_amount和user_total_recharge_times
    if user_referrer is not None and user_referrer.referrer_id != 0:
        db.session.execute(update(UserReferrer).where(UserReferrer.id == user_referrer.id).values(
            user_total_recharge_amount=UserReferrer.user_total_recharge_amount + payment_flow.actual_fee,
            user_total_recharge_times=UserReferrer.user_total_recharge_times + 1,
            update_time=time.time())
        )

        # 实时清分
        # 1、根据user_referrer.referrer_id查询distributor判断是一级分销还是二级分销
        distributor = Distributor.query.filter_by(user_id=user_referrer.referrer_id).first()
        if distributor is not None:
            # 一级分销，只分给一级分销商
            if distributor.parent_user_id == 0:
                commission_record = DistributorCommissionRecords(distributor.id, distributor_order.distributor_order_no, code_gen_util.get_uuid(distributor.user_id),
                                                                 payment_flow.pay_time.timestamp(), payment_flow.pay_date, payment_flow.actual_fee * distributor.commission_rate / 100,
                                                                 distributor.commission_rate, payment_flow.pay_client_type, 1)
                db.session.add(commission_record)
                db.session.flush()
            else:
                # 二级分销，需要分给一级分销商和二级分销商
                # 1、计算分销佣金，单位为分，四舍五入。
                commission_amount = round(payment_flow.actual_fee * distributor.commission_rate / 100)
                # 2、计算管理费给上级
                management_fee_amount = round(commission_amount * MANAGEMENT_FEE_RATE / 100)
                # 计算分销员的佣金
                distributor_commission_amount = round(commission_amount - management_fee_amount)

                # 写入分销商的佣金记录
                commission_record = DistributorCommissionRecords(distributor.id, distributor_order.distributor_order_no, code_gen_util.get_uuid(distributor.user_id),
                                                                 payment_flow.pay_time.timestamp(), payment_flow.pay_date, distributor_commission_amount,
                                                                 distributor.commission_rate, payment_flow.pay_client_type, 1)
                db.session.add(commission_record)
                db.session.flush()

                # 写上上级的管理费记录
                upper_distributor = Distributor.query.filter_by(user_id=distributor.parent_user_id).first()
                if upper_distributor is not None:
                    management_fee_record = DistributorCommissionRecords(upper_distributor.id, distributor_order.distributor_order_no, code_gen_util.get_uuid(upper_distributor.user_id),
                                                                         payment_flow.pay_time.timestamp(), payment_flow.pay_date, management_fee_amount,
                                                                         MANAGEMENT_FEE_RATE, payment_flow.pay_client_type, 2)
                    db.session.add(management_fee_record)
                    db.session.flush()
