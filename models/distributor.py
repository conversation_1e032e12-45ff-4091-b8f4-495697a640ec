from db import db
import time


class Distributor(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    # 姓名
    name = db.Column(db.String(100))
    # 手机号
    phone = db.Column(db.String(100))
    # 企业统一社会信用代码
    unified_social_credit_code = db.Column(db.String(100))
    # 营业执照
    business_license_image_url = db.Column(db.String(100))
    # 企业法人
    legal_entity = db.Column(db.String(100))
    # 对应的用户ID
    user_id = db.Column(db.Integer)
    province_code = db.Column(db.String(100))
    city_code = db.Column(db.String(100))
    # 上级用户
    parent_user_id = db.Column(db.Integer)
    # 组织代码(默认为0000)
    organization_code = db.Column(db.String(100))
    # 分销员类型：1 个人、2 企业
    distributor_type = db.Column(db.Integer)
    # 微信appid
    appid = db.Column(db.String(100))
    # 微信openid(用于微信商户转账到用户余额)
    wechat_openid = db.Column(db.String(100))
    # 收款银行编码
    bank_code = db.Column(db.String(100))
    # 银行名称
    bank_name = db.Column(db.String(100))
    # 收款人姓名
    bank_account_name = db.Column(db.String(100))
    # 收款人账号
    bank_account_no = db.Column(db.String(100))
    # 身份证号
    id_card_no = db.Column(db.String(100))
    # 身份证正面照片
    id_card_front_image_url = db.Column(db.String(100))
    # 身份证反面照片
    id_card_back_image_url = db.Column(db.String(100))
    # 是否已设置收款账户 1 是 0 否
    is_set_bank_account = db.Column(db.Integer)
    # 结算方式 1 转账到微信余额、 2打款到银行卡
    settlement_type = db.Column(db.Integer)
    # 是否已完成实名认证 1 是 0 否
    is_real_name_authentication = db.Column(db.Integer)
    # 状态 1 待审核、2 审核通过、3 审核不通过、 4 已停用
    status = db.Column(db.Integer)
    # 已邀请人数
    invited_count = db.Column(db.Integer)
    # 待结算金额,单位：分
    unsettled_amount = db.Column(db.Integer)
    # 已结算金额,单位：分
    settled_amount = db.Column(db.Integer)
    # 贡献管理费,单位：分
    contribution_management_fee = db.Column(db.Integer)
    # 分销订单数
    distributor_order_count = db.Column(db.Integer)
    # 申请时间
    apply_time = db.Column(db.Integer)
    # 审核时间
    audit_time = db.Column(db.Integer)
    create_time = db.Column(db.Integer)
    update_time = db.Column(db.Integer)

    def __init__(self, name, phone, user_id ,province_code, city_code, parent_user_id, organization_code, distributor_type):
        current_time = time.time()
        self.name = name
        self.phone = phone
        self.user_id = user_id
        self.audit_time = 0
        self.apply_time = current_time
        self.create_time = current_time
        self.update_time = current_time
        self.province_code = province_code
        self.city_code = city_code
        self.parent_user_id = parent_user_id
        self.organization_code = organization_code
        self.distributor_type = distributor_type
        self.appid = ''
        self.wechat_openid = ''
        self.unified_social_credit_code = ''
        self.business_license_image_url = ''
        self.legal_entity = ''
        self.appid = ''
        self.wechat_open_id = ''
        self.bank_code = ''
        self.bank_name = ''
        self.bank_account_name = ''
        self.bank_account_no = ''
        self.id_card_no = ''
        self.status = 0
        self.settlement_type = 0
        self.is_set_bank_account = 0
        self.is_real_name_authentication = 0
        self.id_card_front_image_url = ''
        self.id_card_back_image_url = ''
        self.invited_count = 0
        self.unsettled_amount = 0
        self.settled_amount = 0
        self.contribution_management_fee = 0
        self.distributor_order_count = 0
