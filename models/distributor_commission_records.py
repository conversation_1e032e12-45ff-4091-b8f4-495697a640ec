from db import db

import time

class DistributorCommissionRecords(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    # 分销商用户id
    distributor_user_id = db.Column(db.Integer)
    # 分销订单号
    distributor_order_no = db.Column(db.String)
    # 佣金单号
    commission_no = db.Column(db.String(100))
    # 支付时间
    pay_time = db.Column(db.Integer)
    # 支付日期,格式为20210801
    pay_date = db.Column(db.Integer)
    # 分成金额
    commission_amount = db.Column(db.Integer)
    # 分成比例，整数，例如：20% 为 20
    commission_rate = db.Column(db.Integer)
    # 用户支付方式 支付方式(1:微信 2:支付宝 3:苹果)
    pay_client_type = db.Column(db.Integer)
    # 是否已退款 1 是 0 否
    is_refunded = db.Column(db.Integer)
    # 佣金类型 1 分销佣金 2 团队管理费
    commission_type = db.Column(db.Integer)
    # 结算状态 1 未结算、 2 已结算
    settle_status = db.Column(db.Integer)
    # 分销商结算单id
    settle_no = db.Column(db.String)
    # 结算日期,格式为20210801
    settle_date = db.Column(db.Integer)
    # 结算时间 unix时间戳
    settle_time = db.Column(db.Integer)
    # 创建时间 unix时间戳
    create_time = db.Column(db.Integer)
    # 更新时间 unix时间戳
    update_time = db.Column(db.Integer)

    def __init__(self, distributor_id, distributor_order_no, commission_no,
                 pay_time,pay_date,
                 commission_amount,
                 commission_rate, pay_client_type, commission_type):
        current_time = time.time()
        self.distributor_id = distributor_id
        self.distributor_order_no = distributor_order_no
        self.commission_no = commission_no
        self.pay_time = pay_time
        self.pay_date = pay_date
        self.settle_no = ''
        self.commission_amount = commission_amount
        self.commission_rate = commission_rate
        self.pay_client_type = pay_client_type
        self.commission_type = commission_type
        self.is_refunded = 0
        self.settle_status = 0
        self.settle_date = 0
        self.settle_time = 0
        self.create_time = current_time
        self.update_time = current_time
