from db import db
import time

class DistributorOrder(db.Model):
    '''
    分销商订单表（都是支付完成的订单）
    '''
    id = db.Column(db.Integer, primary_key=True)
    distributor_user_id = db.Column(db.Integer)
    out_trade_no = db.Column(db.String)
    # 分销订单号
    distributor_order_no = db.Column(db.String(100))
    # 订单金额,单位：分
    order_amount = db.Column(db.Integer)
    # 实付金额,单位：分
    actual_fee = db.Column(db.Integer)
    # 优惠金额,单位：分
    discount_amount = db.Column(db.Integer)
    # 结算状态 1 未结算、 2 已结算
    settle_status = db.Column(db.Integer)
    # 分销商结算单号
    settle_no = db.Column(db.String)
    # 结算日期,格式为20210801
    settle_date = db.Column(db.Integer)
    # 结算时间 unix时间戳
    settle_time = db.Column(db.Integer)
    # 支付时间
    pay_time = db.Column(db.Integer)
    # pay_date
    pay_date = db.Column(db.Integer)
    # 创建时间 unix时间戳
    create_time = db.Column(db.Integer)
    # 更新时间 unix时间戳
    update_time = db.Column(db.Integer)

    def __init__(self, distributor_user_id, out_trade_no, distributor_order_no, order_amount, actual_fee, discount_amount, pay_time, pay_date):
        current_time = time.time()
        self.distributor_user_id = distributor_user_id
        self.out_trade_no = out_trade_no
        self.distributor_order_no = distributor_order_no
        self.order_amount = order_amount
        self.actual_fee = actual_fee
        self.discount_amount = discount_amount
        self.settle_status = 1
        self.settle_no = ''
        self.settle_date = 0
        self.settle_time = 0
        self.pay_time = pay_time
        self.pay_date = pay_date
        self.create_time = current_time
        self.update_time = current_time
